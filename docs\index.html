<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShieldsGF Portal - Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="assets/css/docs.css" rel="stylesheet">
</head>
<body class="loading">
    <!-- Loading Screen -->
    <div id="loader" class="loader-overlay">
        <div class="loader-content">
            <div class="loader-spinner"></div>
            <h3>Loading Documentation...</h3>
        </div>
    </div>

    <!-- Custom Cursor -->
    <div class="cursor-holder d-none d-xl-block">
        <div class="cursor"></div>
        <div class="cursor"></div>
    </div>

    <!-- Header -->
    <header class="docs-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="logo-section">
                        <h1 class="docs-title">
                            <i class="fas fa-shield-alt me-2"></i>
                            ShieldsGF Portal
                            <span class="docs-subtitle">Documentation</span>
                        </h1>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="header-actions">
                        <div class="search-container">
                            <input type="text" id="searchInput" class="search-input" placeholder="Search documentation...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                        <button class="theme-toggle" id="themeToggle">
                            <i class="fas fa-moon"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="docs-container">
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar Navigation -->
                <div class="col-lg-3 col-xl-2">
                    <nav class="docs-sidebar">
                        <div class="sidebar-header">
                            <h5>Table of Contents</h5>
                            <button class="sidebar-toggle d-lg-none" id="sidebarToggle">
                                <i class="fas fa-bars"></i>
                            </button>
                        </div>
                        <div class="sidebar-content" id="sidebarContent">
                            <!-- Navigation will be populated by JavaScript -->
                        </div>
                    </nav>
                </div>

                <!-- Main Content Area -->
                <div class="col-lg-9 col-xl-10">
                    <main class="docs-main">
                        <!-- Breadcrumb -->
                        <nav aria-label="breadcrumb" class="docs-breadcrumb">
                            <ol class="breadcrumb" id="breadcrumb">
                                <li class="breadcrumb-item"><a href="#overview">Documentation</a></li>
                            </ol>
                        </nav>

                        <!-- Content Area -->
                        <div class="docs-content" id="docsContent">
                            <!-- Content will be populated by JavaScript -->
                        </div>

                        <!-- Navigation Footer -->
                        <div class="docs-navigation-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="nav-previous" id="navPrevious">
                                        <!-- Previous link will be populated by JavaScript -->
                                    </div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <div class="nav-next" id="navNext">
                                        <!-- Next link will be populated by JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </main>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Search Results Modal -->
    <div class="modal fade" id="searchModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Search Results</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="searchResults">
                        <!-- Search results will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Confetti Canvas -->
    <canvas id="confettiBox"></canvas>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/tsparticles-confetti@2.10.1/tsparticles.confetti.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="assets/js/markdown-content.js"></script>
    <script>
        // Simple and reliable documentation system
        let currentSections = [];

        $(document).ready(function() {
            console.log('🚀 Starting documentation system...');

            // Load saved theme
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                $('body').attr('data-theme', 'dark');
                $('#themeToggle i').removeClass('fa-moon').addClass('fa-sun');
            }

            // Initialize everything
            initializeDocumentation();
        });

        function initializeDocumentation() {
            console.log('📚 Initializing documentation...');

            // Setup all event listeners first
            setupAllEventListeners();

            // Load and process content
            loadAndProcessContent();

            // Hide loader after everything is ready
            setTimeout(hideLoader, 1500);
        }

        function loadAndProcessContent() {
            try {
                console.log('📄 Loading markdown content...');

                // Check if markdown content is available
                if (typeof markdownContent === 'undefined') {
                    throw new Error('Markdown content not loaded - check markdown-content.js');
                }

                console.log('✅ Markdown loaded, length:', markdownContent.length);

                // Parse markdown to HTML using marked
                const htmlContent = marked.parse(markdownContent);
                console.log('✅ Markdown parsed to HTML');

                // Extract sections for navigation
                currentSections = extractSectionsFromMarkdown(markdownContent);
                console.log('✅ Extracted sections:', currentSections.length);

                // Render navigation
                renderNavigationSidebar(currentSections);
                console.log('✅ Navigation rendered');

                // Display content
                displayContent(htmlContent);
                console.log('✅ Content displayed');

                // Add section IDs for navigation
                addSectionIds();
                console.log('✅ Section IDs added');

                // Trigger confetti for welcome
                setTimeout(triggerConfetti, 2000);

            } catch (error) {
                console.error('❌ Error loading documentation:', error);
                showErrorMessage(error.message);
            }
        }

        function extractSectionsFromMarkdown(markdownText) {
            const sections = [];
            const lines = markdownText.split('\n');

            lines.forEach((line, index) => {
                const headerMatch = line.match(/^(#{1,6})\s+(.+)$/);
                if (headerMatch) {
                    const level = headerMatch[1].length;
                    let title = headerMatch[2].trim();

                    // Clean title for display
                    const cleanTitle = title
                        .replace(/\*\*/g, '')
                        .replace(/\*/g, '')
                        .replace(/📚|🚀|👥|🎯|🔧|📱|🏗️|🔒|📊|🛠️/g, '')
                        .trim();

                    // Create ID
                    const id = cleanTitle.toLowerCase()
                        .replace(/[^\w\s-]/g, '')
                        .replace(/\s+/g, '-')
                        .replace(/-+/g, '-')
                        .replace(/^-|-$/g, '');

                    if (cleanTitle && id) {
                        sections.push({
                            level: level,
                            title: cleanTitle,
                            id: id,
                            line: index
                        });
                    }
                }
            });

            return sections;
        }

        function renderNavigationSidebar(sections) {
            const $sidebar = $('#sidebarContent');
            $sidebar.empty();

            // Create main navigation group
            const $navSection = $(`
                <div class="nav-section">
                    <div class="nav-section-title">
                        <i class="fas fa-book"></i>
                        Documentation
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </div>
                    <ul class="nav-items">
                    </ul>
                </div>
            `);

            // Add sections (limit to first 30 for performance)
            const limitedSections = sections.slice(0, 30);
            limitedSections.forEach(section => {
                if (section.level <= 3) { // Only show H1, H2, H3
                    const indent = section.level > 1 ? 'style="padding-left: ' + (section.level * 10) + 'px;"' : '';
                    const $item = $(`
                        <li class="nav-item">
                            <a href="#${section.id}" class="nav-link" data-section="${section.id}" ${indent}>
                                <i class="fas fa-file-text"></i>
                                ${section.title}
                            </a>
                        </li>
                    `);
                    $navSection.find('.nav-items').append($item);
                }
            });

            $sidebar.append($navSection);
        }

        function displayContent(htmlContent) {
            $('#docsContent').html(htmlContent);

            // Update breadcrumb
            $('#breadcrumb').html(`
                <li class="breadcrumb-item active">ShieldsGF Portal Documentation</li>
            `);

            // Highlight code blocks
            if (typeof Prism !== 'undefined') {
                Prism.highlightAll();
            }
        }

        function addSectionIds() {
            // Add IDs to headers for navigation
            currentSections.forEach(section => {
                $(`h${section.level}`).each(function() {
                    const headerText = $(this).text().trim();
                    const cleanText = headerText
                        .replace(/\*\*/g, '')
                        .replace(/\*/g, '')
                        .replace(/📚|🚀|👥|🎯|🔧|📱|🏗️|🔒|📊|🛠️/g, '')
                        .trim();

                    if (cleanText === section.title) {
                        $(this).attr('id', section.id);
                    }
                });
            });
        }

        function setupAllEventListeners() {
            console.log('🎯 Setting up event listeners...');

            // Navigation clicks
            $(document).on('click', '.nav-link', function(e) {
                e.preventDefault();
                const sectionId = $(this).data('section');
                console.log('🔗 Navigation clicked:', sectionId);
                scrollToSection(sectionId);

                // Update active state
                $('.nav-link').removeClass('active');
                $(this).addClass('active');
            });

            // Section title clicks (expand/collapse)
            $(document).on('click', '.nav-section-title', function(e) {
                const $section = $(this);
                const $items = $section.next('.nav-items');

                $section.toggleClass('collapsed');
                $items.toggleClass('collapsed');
                console.log('📂 Section toggled');
            });

            // Theme toggle
            $('#themeToggle').on('click', function() {
                toggleTheme();
                triggerConfetti(); // Confetti on theme change!
            });

            // Back to top
            $('#backToTop').on('click', function() {
                $('html, body').animate({ scrollTop: 0 }, 600);
                triggerConfetti(); // Confetti when going to top!
            });

            // Sidebar toggle for mobile
            $('#sidebarToggle').on('click', function() {
                $('.docs-sidebar').toggleClass('show');
            });

            // Search functionality
            $('#searchInput').on('input', function() {
                const query = $(this).val();
                if (query.length > 2) {
                    performSearch(query);
                } else {
                    clearSearch();
                }
            });

            // Scroll effects
            $(window).on('scroll', function() {
                const scrollTop = $(window).scrollTop();

                // Back to top button
                if (scrollTop > 300) {
                    $('#backToTop').addClass('visible');
                } else {
                    $('#backToTop').removeClass('visible');
                }

                // Confetti on reaching bottom
                if (scrollTop + $(window).height() >= $(document).height() - 100) {
                    triggerConfetti();
                }
            });

            // Close sidebar on outside click (mobile)
            $(document).on('click', function(e) {
                if ($(window).width() < 992) {
                    if (!$(e.target).closest('.docs-sidebar, #sidebarToggle').length) {
                        $('.docs-sidebar').removeClass('show');
                    }
                }
            });

            console.log('✅ All event listeners set up');
        }

        function scrollToSection(sectionId) {
            const $target = $('#' + sectionId);
            if ($target.length) {
                $('html, body').animate({
                    scrollTop: $target.offset().top - 100
                }, 500);

                // Update URL
                window.history.pushState({}, '', '#' + sectionId);

                // Close mobile sidebar
                if ($(window).width() < 992) {
                    $('.docs-sidebar').removeClass('show');
                }

                console.log('📍 Scrolled to section:', sectionId);
            } else {
                console.warn('⚠️ Section not found:', sectionId);
            }
        }

        function performSearch(query) {
            const $content = $('#docsContent');
            const content = $content.html();

            // Remove previous highlights
            const cleanContent = content.replace(/<mark class="search-highlight">(.*?)<\/mark>/gi, '$1');

            // Add new highlights
            const regex = new RegExp('(' + query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + ')', 'gi');
            const highlightedContent = cleanContent.replace(regex, '<mark class="search-highlight">$1</mark>');

            $content.html(highlightedContent);
            console.log('🔍 Search performed for:', query);
        }

        function clearSearch() {
            const $content = $('#docsContent');
            const content = $content.html();
            const cleanContent = content.replace(/<mark class="search-highlight">(.*?)<\/mark>/gi, '$1');
            $content.html(cleanContent);
        }

        function toggleTheme() {
            const $body = $('body');
            const $icon = $('#themeToggle i');

            if ($body.attr('data-theme') === 'dark') {
                $body.removeAttr('data-theme');
                $icon.removeClass('fa-sun').addClass('fa-moon');
                localStorage.setItem('theme', 'light');
                console.log('🌞 Switched to light theme');
            } else {
                $body.attr('data-theme', 'dark');
                $icon.removeClass('fa-moon').addClass('fa-sun');
                localStorage.setItem('theme', 'dark');
                console.log('🌙 Switched to dark theme');
            }
        }

        function triggerConfetti() {
            if (typeof confetti !== 'undefined') {
                confetti({
                    particleCount: 100,
                    spread: 70,
                    origin: { y: 0.6 },
                    colors: ['#ff6b35', '#f39c12', '#e74c3c', '#9b59b6', '#3498db', '#2ecc71']
                });
                console.log('🎉 Confetti triggered!');
            } else {
                console.log('🎊 Confetti library not loaded');
            }
        }

        function showErrorMessage(message) {
            $('#docsContent').html(`
                <div class="alert alert-danger">
                    <h4><i class="fas fa-exclamation-triangle"></i> Error Loading Documentation</h4>
                    <p>Could not load the documentation content.</p>
                    <p><strong>Error:</strong> ${message}</p>
                    <hr>
                    <p><strong>Troubleshooting:</strong></p>
                    <ul>
                        <li>Make sure the <code>markdown-content.js</code> file is properly loaded</li>
                        <li>Check the browser console for additional error details</li>
                        <li>Try refreshing the page</li>
                    </ul>
                </div>
            `);
        }

        function hideLoader() {
            $('#loader').addClass('hidden');
            $('body').removeClass('loading');
            console.log('🎯 Loader hidden, documentation ready!');

            // Welcome confetti
            setTimeout(triggerConfetti, 500);
        }

        // Handle initial hash navigation
        $(window).on('load', function() {
            const hash = window.location.hash.substring(1);
            if (hash) {
                setTimeout(function() {
                    scrollToSection(hash);
                    $(`.nav-link[data-section="${hash}"]`).addClass('active');
                }, 2000);
            }
        });
    </script>
</body>
</html>
