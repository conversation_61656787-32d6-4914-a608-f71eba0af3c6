// ShieldsGF Portal Documentation JavaScript

class DocsApp {
    constructor() {
        this.currentSection = 'overview';
        this.searchIndex = [];
        this.isLoading = true;
        
        this.init();
    }

    init() {
        console.log('Initializing DocsApp...');
        console.log('Available documentation sections:', Object.keys(docsData));

        this.setupEventListeners();
        this.initializeCustomCursor();
        this.buildSearchIndex();
        this.renderNavigation();

        // Load initial section
        setTimeout(() => {
            this.loadSection('overview');
        }, 100);

        this.setupScrollEffects();
        this.hideLoader();
    }

    setupEventListeners() {
        // Search functionality
        $('#searchInput').on('input', (e) => {
            this.handleSearch(e.target.value);
        });

        // Theme toggle
        $('#themeToggle').on('click', () => {
            this.toggleTheme();
        });

        // Sidebar toggle for mobile
        $('#sidebarToggle').on('click', () => {
            $('.docs-sidebar').toggleClass('show');
        });

        // Back to top button
        $('#backToTop').on('click', () => {
            $('html, body').animate({ scrollTop: 0 }, 600);
        });

        // Handle navigation clicks
        $(document).on('click', '.nav-link', (e) => {
            e.preventDefault();
            const $target = $(e.currentTarget);
            const sectionId = $target.data('section');
            console.log('Navigation clicked:', sectionId);
            this.loadSection(sectionId);
        });

        // Handle section title clicks
        $(document).on('click', '.nav-section-title', (e) => {
            const $section = $(e.currentTarget);
            const $items = $section.next('.nav-items');
            
            $section.toggleClass('collapsed');
            $items.toggleClass('collapsed');
        });

        // Handle scroll events
        $(window).on('scroll', () => {
            this.handleScroll();
        });

        // Handle window resize
        $(window).on('resize', () => {
            this.handleResize();
        });

        // Close sidebar on outside click (mobile)
        $(document).on('click', (e) => {
            if ($(window).width() < 992) {
                if (!$(e.target).closest('.docs-sidebar, #sidebarToggle').length) {
                    $('.docs-sidebar').removeClass('show');
                }
            }
        });
    }

    initializeCustomCursor() {
        if ($(window).width() >= 1200) {
            const cursors = document.querySelectorAll('.cursor');
            
            document.addEventListener('mousemove', (e) => {
                cursors[0].style.left = e.clientX + 'px';
                cursors[0].style.top = e.clientY + 'px';
                
                setTimeout(() => {
                    cursors[1].style.left = e.clientX - 16 + 'px';
                    cursors[1].style.top = e.clientY - 16 + 'px';
                }, 100);
            });

            // Cursor hover effects
            $('a, button, .nav-link').on('mouseenter', () => {
                cursors.forEach(cursor => cursor.style.transform = 'scale(1.5)');
            }).on('mouseleave', () => {
                cursors.forEach(cursor => cursor.style.transform = 'scale(1)');
            });
        }
    }

    buildSearchIndex() {
        this.searchIndex = [];
        
        Object.keys(docsData).forEach(key => {
            const section = docsData[key];
            
            // Index main content
            this.searchIndex.push({
                id: section.id,
                title: section.title,
                content: this.stripHtml(section.content),
                type: 'section'
            });

            // Index subsections
            if (section.subsections) {
                section.subsections.forEach(subsection => {
                    this.searchIndex.push({
                        id: subsection.id,
                        title: subsection.title,
                        content: '',
                        type: 'subsection',
                        parent: section.id
                    });
                });
            }
        });
    }

    stripHtml(html) {
        const tmp = document.createElement('div');
        tmp.innerHTML = html;
        return tmp.textContent || tmp.innerText || '';
    }

    renderNavigation() {
        const $sidebar = $('#sidebarContent');
        $sidebar.empty();

        navigationStructure.forEach(section => {
            const $section = $(`
                <div class="nav-section">
                    <div class="nav-section-title" data-section="${section.id}">
                        <i class="${section.icon}"></i>
                        ${section.title}
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </div>
                    <ul class="nav-items">
                        ${section.items.map(item => `
                            <li class="nav-item">
                                <a href="#${item.id}" class="nav-link" data-section="${item.id}">
                                    <i class="${item.icon}"></i>
                                    ${item.title}
                                </a>
                            </li>
                        `).join('')}
                    </ul>
                </div>
            `);
            
            $sidebar.append($section);
        });
    }

    loadSection(sectionId) {
        console.log('Loading section:', sectionId);
        console.log('Available sections:', Object.keys(docsData));

        if (!docsData[sectionId]) {
            console.warn(`Section ${sectionId} not found`);
            // Load overview as fallback
            if (sectionId !== 'overview' && docsData['overview']) {
                this.loadSection('overview');
            }
            return;
        }

        this.currentSection = sectionId;
        const section = docsData[sectionId];
        console.log('Loading section data:', section);

        // Update active navigation
        $('.nav-link').removeClass('active');
        $(`.nav-link[data-section="${sectionId}"]`).addClass('active');

        // Update breadcrumb
        this.updateBreadcrumb(section);

        // Load content with animation
        const $content = $('#docsContent');
        $content.fadeOut(200, () => {
            $content.html(section.content);

            // Highlight code blocks
            if (typeof Prism !== 'undefined') {
                Prism.highlightAll();
            }

            // Fade in content
            $content.fadeIn(400);

            // Scroll to top
            $('html, body').animate({ scrollTop: 0 }, 300);

            // Update URL
            window.history.pushState({}, '', `#${sectionId}`);

            // Update navigation footer
            this.updateNavigationFooter(sectionId);
        });

        // Close sidebar on mobile after navigation
        if ($(window).width() < 992) {
            $('.docs-sidebar').removeClass('show');
        }

        // Trigger confetti for certain sections
        if (['overview', 'gettingStarted'].includes(sectionId)) {
            setTimeout(() => this.triggerConfetti(), 1000);
        }
    }

    updateBreadcrumb(section) {
        const $breadcrumb = $('#breadcrumb');
        $breadcrumb.html(`
            <li class="breadcrumb-item"><a href="#overview">Documentation</a></li>
            <li class="breadcrumb-item active">${section.title}</li>
        `);
    }

    updateNavigationFooter(currentSectionId) {
        const allSections = Object.keys(docsData);
        const currentIndex = allSections.indexOf(currentSectionId);
        
        const $navPrevious = $('#navPrevious');
        const $navNext = $('#navNext');
        
        $navPrevious.empty();
        $navNext.empty();

        // Previous section
        if (currentIndex > 0) {
            const prevSection = docsData[allSections[currentIndex - 1]];
            $navPrevious.html(`
                <a href="#${prevSection.id}" class="nav-link" data-section="${prevSection.id}">
                    <div class="nav-label">Previous</div>
                    <div class="nav-title">
                        <i class="fas fa-arrow-left me-2"></i>
                        ${prevSection.title}
                    </div>
                </a>
            `);
        }

        // Next section
        if (currentIndex < allSections.length - 1) {
            const nextSection = docsData[allSections[currentIndex + 1]];
            $navNext.html(`
                <a href="#${nextSection.id}" class="nav-link" data-section="${nextSection.id}">
                    <div class="nav-label">Next</div>
                    <div class="nav-title">
                        ${nextSection.title}
                        <i class="fas fa-arrow-right ms-2"></i>
                    </div>
                </a>
            `);
        }
    }

    handleSearch(query) {
        if (query.length < 2) {
            return;
        }

        const results = this.searchIndex.filter(item => {
            return item.title.toLowerCase().includes(query.toLowerCase()) ||
                   item.content.toLowerCase().includes(query.toLowerCase());
        }).slice(0, 10);

        this.displaySearchResults(results, query);
    }

    displaySearchResults(results, query) {
        const $modal = $('#searchModal');
        const $results = $('#searchResults');
        
        if (results.length === 0) {
            $results.html('<p class="text-muted">No results found.</p>');
        } else {
            const resultsHtml = results.map(result => {
                const highlightedTitle = this.highlightText(result.title, query);
                const highlightedContent = this.highlightText(
                    result.content.substring(0, 200) + '...', 
                    query
                );
                
                return `
                    <div class="search-result-item" data-section="${result.id}">
                        <div class="search-result-title">${highlightedTitle}</div>
                        <div class="search-result-content">${highlightedContent}</div>
                    </div>
                `;
            }).join('');
            
            $results.html(resultsHtml);
        }
        
        $modal.modal('show');
        
        // Handle result clicks
        $('.search-result-item').on('click', (e) => {
            const sectionId = $(e.currentTarget).data('section');
            this.loadSection(sectionId);
            $modal.modal('hide');
        });
    }

    highlightText(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<span class="search-highlight">$1</span>');
    }

    toggleTheme() {
        const $body = $('body');
        const $icon = $('#themeToggle i');
        
        if ($body.attr('data-theme') === 'dark') {
            $body.removeAttr('data-theme');
            $icon.removeClass('fa-sun').addClass('fa-moon');
            localStorage.setItem('theme', 'light');
        } else {
            $body.attr('data-theme', 'dark');
            $icon.removeClass('fa-moon').addClass('fa-sun');
            localStorage.setItem('theme', 'dark');
        }
    }

    setupScrollEffects() {
        // Back to top button visibility
        $(window).on('scroll', () => {
            const $backToTop = $('#backToTop');
            if ($(window).scrollTop() > 300) {
                $backToTop.addClass('visible');
            } else {
                $backToTop.removeClass('visible');
            }
        });

        // Confetti on scroll to bottom
        let confettiTriggered = false;
        $(window).on('scroll', () => {
            if (!confettiTriggered && $(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                this.triggerConfetti();
                confettiTriggered = true;
                setTimeout(() => confettiTriggered = false, 5000);
            }
        });
    }

    handleScroll() {
        // Add any scroll-based animations or effects here
    }

    handleResize() {
        // Handle responsive behavior
        if ($(window).width() >= 992) {
            $('.docs-sidebar').removeClass('show');
        }
    }

    triggerConfetti() {
        if (typeof confetti !== 'undefined') {
            confetti({
                particleCount: 100,
                spread: 70,
                origin: { y: 0.6 },
                colors: ['#ff6b35', '#f39c12', '#e74c3c', '#9b59b6', '#3498db']
            });
        }
    }

    hideLoader() {
        setTimeout(() => {
            $('#loader').addClass('hidden');
            $('body').removeClass('loading');
        }, 1000);
    }
}

// Initialize the documentation app when DOM is ready
$(document).ready(() => {
    // Load saved theme
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        $('body').attr('data-theme', 'dark');
        $('#themeToggle i').removeClass('fa-moon').addClass('fa-sun');
    }

    // Initialize the app
    window.docsApp = new DocsApp();

    // Handle initial hash
    const hash = window.location.hash.substring(1);
    console.log('Initial hash:', hash);
    if (hash && docsData[hash]) {
        setTimeout(() => {
            window.docsApp.loadSection(hash);
        }, 200);
    }
});
