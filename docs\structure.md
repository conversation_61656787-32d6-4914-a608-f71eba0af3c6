# ShieldsGF Portal - Complete Documentation

## Table of Contents

### 📚 **1. Introduction & Overview**

- What is ShieldsGF Portal?
- Key Features & Benefits
- System Architecture Overview
- User Roles & Permissions Matrix

### 🚀 **2. Getting Started**

- System Requirements
- Installation Guide
- Initial Configuration
- First Login & Setup

### 👥 **3. User Roles & Access Control**

- Role Hierarchy Explained
- Permission Matrix
- Dashboard Variations by Role
- Access Level Management

### 🎯 **4. Core Features Deep Dive**

- **4.1 Project Management Lifecycle**
- **4.2 Task Management System**
- **4.3 Status Hub - Visual Project Tracking**
- **4.4 PTO Calendar & Holiday Management**
- **4.5 Resource Allocation & Planning**
- **4.6 Team Collaboration Tools**
- **4.7 File Management System**
- **4.8 Messaging & Communication**

### 🔧 **5. Administrative Functions**

- Admin Dashboard Overview
- User Management
- System Configuration
- Holiday & PTO Administration
- Resource Planning & Allocation

### 📱 **6. Client & Brand Portals**

- Client Dashboard Features
- Brand/Enterprise Portal
- Project Visibility & Communication
- File Access & Collaboration

### 🔌 **7. Integrations & APIs**

- Harvest Time Tracking Integration
- Email Notification System
- API Endpoints Reference
- Third-party Integrations

### 🛠️ **8. Technical Documentation**

- Database Schema & Relationships
- Livewire Components Architecture
- Security Implementation
- File Structure & Organization

### 🆘 **9. Support & Troubleshooting**

- Common Issues & Solutions
- Error Messages Reference
- FAQ Section
- Support Contacts

---

## 📚 **1. Introduction & Overview**

### What is ShieldsGF Portal?

ShieldsGF Portal is a comprehensive project management and team collaboration platform designed to bridge the gap between clients and development teams. It provides a centralized hub for managing projects, tracking progress, allocating resources, and facilitating seamless communication throughout the project lifecycle.

**Core Purpose:**

- Streamline project workflows from inception to completion
- Provide transparent project visibility for all stakeholders
- Optimize resource allocation and team utilization
- Facilitate effective communication between teams and clients
- Maintain comprehensive project documentation and history

### Key Features & Benefits

**🎯 Project Management Excellence**

- Complete project lifecycle management with phase tracking
- Visual project status representation through Status Hub
- Drag-and-drop project phase transitions
- Real-time progress monitoring and reporting
- Client-specific project portals for transparency

**👥 Advanced Team Collaboration**

- Role-based access control with granular permissions
- Rich messaging system with file attachments
- User mentions and notification system
- Team resource allocation and workload balancing
- Cross-functional team coordination tools

**📅 Smart Resource Planning**

- Weekly resource allocation tracking
- PTO and holiday management integration
- Multi-role hour allocation (Developer, Designer, PM)
- Utilization percentage calculations
- Extended timeline planning beyond current periods

**🔧 Administrative Control**

- Comprehensive user management system
- Flexible role and permission configuration
- System-wide holiday and PTO administration
- Integration with external tools (Harvest time tracking)
- Detailed reporting and analytics

### System Architecture Overview

**Technology Stack:**

- **Backend:** Laravel 12.x (PHP 8.2+)
- **Frontend:** Livewire 3.6+ for reactive components
- **Database:** MySQL 8.0+ with optimized relationships
- **Real-time Communication:** Laravel Reverb 1.5+ (WebSocket server)
- **Authentication:** Laravel's built-in authentication system
- **PDF Generation:** Spatie Laravel PDF 1.5+
- **Permissions:** Spatie Laravel Permission 6.16+
- **Asset Compilation:** Vite for modern asset bundling
- **File Storage:** Configurable storage system (local/S3/cloud)
- **Queue System:** Database/Redis queue support
- **Broadcasting:** Laravel Echo with Pusher.js integration

**Architecture Principles:**

- **MVC Pattern:** Clean separation of concerns
- **Component-Based:** Reusable Livewire components
- **Role-Based Security:** Multi-layered permission system
- **Scalable Design:** Optimized for growing teams and projects
- **Integration-Ready:** API endpoints for external tool integration

### User Roles & Permissions Matrix

| Role                          | Project Management | User Management | System Config  | Resource Planning | Client Communication |
| ----------------------------- | ------------------ | --------------- | -------------- | ----------------- | -------------------- |
| **SuperAdmin**                | ✅ Full Access     | ✅ Full Access  | ✅ Full Access | ✅ Full Access    | ✅ Full Access       |
| **Admin**                     | ✅ Full Access     | ✅ Limited      | ❌ No Access   | ✅ Full Access    | ✅ Full Access       |
| **Team Member**               | ✅ Assigned Only   | ❌ No Access    | ❌ No Access   | ✅ View Only      | ✅ Project-Based     |
| **Team Member (Admin Level)** | ✅ Full Access     | ✅ Limited      | ❌ No Access   | ✅ Full Access    | ✅ Full Access       |
| **Client Member**             | ✅ Own Projects    | ❌ No Access    | ❌ No Access   | ❌ No Access      | ✅ Own Projects      |
| **Enterprise Owner**          | ✅ Brand Projects  | ❌ No Access    | ❌ No Access   | ❌ No Access      | ✅ Brand Projects    |

---

## 🚀 **2. Getting Started**

### System Requirements

**Server Requirements:**

- **PHP:** 8.1 or higher with required extensions
- **Web Server:** Apache 2.4+ or Nginx 1.18+
- **Database:** MySQL 8.0+ or MariaDB 10.4+
- **Memory:** Minimum 512MB RAM (2GB+ recommended)
- **Storage:** 1GB+ available disk space

**Development Environment:**

- **Node.js:** 16.x or higher for asset compilation
- **Composer:** Latest version for PHP dependency management
- **Git:** For version control and deployment

**PHP Extensions Required:**

```
- BCMath PHP Extension
- Ctype PHP Extension
- Fileinfo PHP Extension
- JSON PHP Extension
- Mbstring PHP Extension
- OpenSSL PHP Extension
- PDO PHP Extension
- Tokenizer PHP Extension
- XML PHP Extension
```

### Installation Guide

**Step 1: Clone Repository**

```bash
git clone [repository-url] sgf-portal
cd sgf-portal
```

**Step 2: Install Dependencies**

```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

**Step 3: Environment Configuration**

```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

**Step 4: Database Setup**

```bash
# Configure database in .env file
# DB_CONNECTION=mysql
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=sgf_portal
# DB_USERNAME=your_username
# DB_PASSWORD=your_password

# Run migrations
php artisan migrate

# Seed initial data
php artisan db:seed
```

**Step 5: Asset Compilation**

```bash
# For development
npm run dev

# For production
npm run build
```

**Step 6: Storage Configuration**

```bash
# Create storage link
php artisan storage:link

# Set proper permissions
chmod -R 775 storage bootstrap/cache
```

### Initial Configuration

**Environment Variables:**

```env
# Application
APP_NAME="ShieldsGF Portal"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sgf_portal
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# File Storage
FILESYSTEM_DISK=local
# For production, consider: FILESYSTEM_DISK=s3

# Session & Cache
SESSION_DRIVER=file
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
```

### First Login & Setup

**Default Admin Account:**
After running the database seeder, a default SuperAdmin account is created:

- **Email:** <EMAIL>
- **Password:** admin123 (change immediately after first login)

**Initial Setup Steps:**

1. **Login** with default credentials
2. **Change Password** immediately for security
3. **Configure System Settings** in admin panel
4. **Create User Accounts** for team members
5. **Set Up Roles & Permissions** as needed
6. **Configure Integrations** (Harvest, email, etc.)
7. **Create First Project** to test functionality

**Security Checklist:**

- [ ] Change default admin password
- [ ] Configure proper file permissions
- [ ] Set up SSL certificate
- [ ] Configure backup procedures
- [ ] Test email notifications
- [ ] Verify user registration process

---

## 👥 **3. User Roles & Access Control**

### Role Hierarchy Explained

The ShieldsGF Portal implements a sophisticated role-based access control system that ensures appropriate access levels for different user types while maintaining security and operational efficiency.

**🔴 SuperAdmin - System Owner**

- **Purpose:** Complete system control and oversight
- **Scope:** Entire platform and all data
- **Key Responsibilities:**
  - System configuration and maintenance
  - User account creation and management
  - Role and permission administration
  - Database and security oversight
  - Integration management

**🟠 Admin - Project Manager**

- **Purpose:** Project and team management
- **Scope:** All projects and team members
- **Key Responsibilities:**
  - Project creation and lifecycle management
  - Team member assignment and oversight
  - Resource allocation and planning
  - Client communication coordination
  - Performance monitoring and reporting

**🟡 Team Member - Task Executor**

- **Purpose:** Task completion and project contribution
- **Scope:** Assigned projects and tasks
- **Key Responsibilities:**
  - Task execution and status updates
  - Time tracking and reporting
  - Team collaboration and communication
  - File uploads and documentation
  - Personal PTO management

**🟢 Team Member (Admin Access Level) - Enhanced Contributor**

- **Purpose:** Team member with elevated permissions
- **Scope:** Extended project and team visibility
- **Key Responsibilities:**
  - All team member responsibilities
  - Project creation and management
  - Limited user management
  - Resource allocation oversight
  - Advanced reporting access

**🔵 Client Member - Project Stakeholder**

- **Purpose:** Project visibility and communication
- **Scope:** Own projects and related communications
- **Key Responsibilities:**
  - Project progress monitoring
  - Communication with project teams
  - File access and review
  - Feedback provision
  - Invoice and billing review

**🟣 Enterprise Owner - Multi-Project Oversight**

- **Purpose:** Brand-level project management
- **Scope:** All projects under their enterprise/brand
- **Key Responsibilities:**
  - Multi-project oversight
  - Brand-level reporting
  - Client relationship management
  - Strategic project planning
  - Resource allocation review

### Permission Matrix

| Feature                   | SuperAdmin | Admin | Team Member | Team Member (Admin) | Client Member | Enterprise Owner |
| ------------------------- | ---------- | ----- | ----------- | ------------------- | ------------- | ---------------- |
| **User Management**       |
| Create Users              | ✅         | ✅    | ❌          | ✅                  | ❌            | ❌               |
| Edit Users                | ✅         | ✅    | ❌          | ✅                  | ❌            | ❌               |
| Delete Users              | ✅         | ❌    | ❌          | ❌                  | ❌            | ❌               |
| Assign Roles              | ✅         | ✅    | ❌          | ✅                  | ❌            | ❌               |
| **Project Management**    |
| Create Projects           | ✅         | ✅    | ❌          | ✅                  | ❌            | ❌               |
| Edit All Projects         | ✅         | ✅    | ❌          | ✅                  | ❌            | ❌               |
| View All Projects         | ✅         | ✅    | ❌          | ✅                  | ❌            | ❌               |
| View Assigned Projects    | ✅         | ✅    | ✅          | ✅                  | ✅            | ✅               |
| Archive Projects          | ✅         | ✅    | ❌          | ✅                  | ❌            | ❌               |
| **Task Management**       |
| Create Tasks              | ✅         | ✅    | ❌          | ✅                  | ❌            | ❌               |
| Edit All Tasks            | ✅         | ✅    | ❌          | ✅                  | ❌            | ❌               |
| View All Tasks            | ✅         | ✅    | ❌          | ✅                  | ❌            | ❌               |
| View Assigned Tasks       | ✅         | ✅    | ✅          | ✅                  | ✅            | ✅               |
| Update Task Status        | ✅         | ✅    | ✅          | ✅                  | ❌            | ❌               |
| **Resource Management**   |
| View Resource Allocation  | ✅         | ✅    | ✅          | ✅                  | ❌            | ❌               |
| Edit Resource Allocation  | ✅         | ✅    | ❌          | ✅                  | ❌            | ❌               |
| Manage Team Resources     | ✅         | ✅    | ❌          | ✅                  | ❌            | ❌               |
| **Calendar & PTO**        |
| Add Personal PTO          | ✅         | ✅    | ✅          | ✅                  | ✅            | ✅               |
| Add Global Holidays       | ✅         | ✅    | ❌          | ❌                  | ❌            | ❌               |
| Manage Team PTO           | ✅         | ✅    | ❌          | ✅                  | ❌            | ❌               |
| View Team Calendar        | ✅         | ✅    | ✅          | ✅                  | ✅            | ✅               |
| **Communication**         |
| Project Messaging         | ✅         | ✅    | ✅          | ✅                  | ✅            | ✅               |
| File Uploads              | ✅         | ✅    | ✅          | ✅                  | ✅            | ✅               |
| Email Notifications       | ✅         | ✅    | ✅          | ✅                  | ✅            | ✅               |
| **System Administration** |
| System Configuration      | ✅         | ❌    | ❌          | ❌                  | ❌            | ❌               |
| Database Management       | ✅         | ❌    | ❌          | ❌                  | ❌            | ❌               |
| Integration Setup         | ✅         | ❌    | ❌          | ❌                  | ❌            | ❌               |

### Dashboard Variations by Role

**SuperAdmin Dashboard:**

```
┌─────────────────────────────────────────────────────────────┐
│ 🏠 Admin Dashboard - Happy [Day], [User Name]              │
├─────────────────────────────────────────────────────────────┤
│ � QUICK LINKS                                              │
│ • Clients List        • Project List       • New Project   │
│ • Enterprises List    • New Client         • New Enterprise│
│ ─────────────────────────────────────────────────────────── │
│ • Team Members        • Status Hub         • Harvest       │
│ • PTO Calendar        • Hub Settings                       │
├─────────────────────────────────────────────────────────────┤
│ � TASKS OVERVIEW                          [See All Tasks] │
│ ⭐ Urgent Tasks (with star icons)                          │
│ • [SP-001-25] Project Name - Task Details                  │
│ • [SP-002-25] Project Name - Task Details                  │
│ 🆕 New Tasks (with alert icons)                           │
│ • [SP-003-25] Project Name - Task Details                  │
│ • [SP-004-25] Project Name - Task Details                  │
│                                        [X MORE +]          │
├─────────────────────────────────────────────────────────────┤
│ � YOUR PROJECTS                        [See All Projects] │
│ • [SP-001] Project Name (40% complete)                     │
│ • [SP-002] Project Name (60% complete)                     │
│ • [SP-003] Project Name (25% complete)                     │
│ • [SP-004] Project Name (80% complete)                     │
│ • [SP-005] Project Name (15% complete)                     │
└─────────────────────────────────────────────────────────────┘
```

**Dashboard Implementation Details:**

**Route Structure:**

- **Route:** `/admin/dashboard` (SuperAdmin/Admin access)
- **Controller:** `AdminDashboardController@dashboard`
- **Middleware:** `auth`, `superAdmin`, `hasPermission:dashboard-view`
- **View:** `resources/views/admin/dashboard/index.blade.php`

**Dashboard Logic:**

```php
public function dashboard()
{
    $user = User::findOrFail(Auth::user()->id);

    $projects = $user->projects()
        ->with(['tasks' => function ($query) use ($user) {
            $query->whereHas('users', function ($q) use ($user) {
                $q->where('users.id', $user->id);
            })->with('status');
        }])
        ->get();

    // Group tasks by status - only show urgent and new tasks
    $groupedTasks = [
        'urgent' => [],
        'regular' => [],
    ];

    foreach ($projects as $project) {
        foreach ($project->tasks as $task) {
            $status = strtolower($task->status->name ?? '');

            if ($status === 'urgent') {
                $groupedTasks['urgent'][] = $task;
            } else if ($status === 'new') {
                $groupedTasks['regular'][] = $task;
            }
        }
    }
}
```

**Quick Links Available:**

1. **Management Links:**

   - Clients List (`/admin/clients`)
   - Enterprises List (`/admin/brands`)
   - Project List (`/admin/projects`)
   - Team Members (`/teams`)

2. **Creation Links:**

   - New Project (`/add-project`)
   - New Client (`/add-client`)
   - New Enterprise (`/add-brand`)

3. **System Links:**
   - Status Hub (`/projects/status`)
   - Harvest (External: `https://shieldssgfinc.harvestapp.com/`)
   - PTO Calendar (`/admin/calender`)
   - Hub Settings (Placeholder link)

**Task Overview Section:**

- **Display Logic:** Shows only 'urgent' and 'new' status tasks
- **Task Limit:** Maximum 5 tasks displayed initially
- **Visual Indicators:**
  - Urgent tasks: Star icon (`star-icon.svg`)
  - New tasks: Alert icon (`alert-icon.svg`)
- **Task Information:**
  - Project job code and name
  - Task creation date
  - Task name (clickable link to task details)
  - Task description (truncated with "Read More" link)
- **More Tasks Link:** Shows remaining task count with link to all tasks

**Your Projects Section:**

- **Display Logic:** Shows first 5 projects assigned to the user
- **Project Information:**
  - Job code in brackets (e.g., `[SP-001]`)
  - Project name (clickable link to edit project)
  - Progress percentage with visual progress bar
  - Project icon based on phase/status
- **Progress Visualization:** Circular progress bars with percentage display

**Team Member Dashboard:**

**Route Structure:**

- **Route:** `/dashboard` (redirects based on role)
- **Controller:** `DashboardController@index`
- **Logic:** Redirects SuperAdmin/Admin to admin dashboard, others to team dashboard
- **View:** `resources/views/dashboard.blade.php`

```
┌─────────────────────────────────────────────────────────────┐
│ 🏠 Team Dashboard - Happy [Day], [User Name]               │
├─────────────────────────────────────────────────────────────┤
│ 📋 QUICK LINKS                                              │
│ • Project List        • Tasks List                         │
│ • Status Hub          • PTO Calendar                       │
├─────────────────────────────────────────────────────────────┤
│ 📊 TASKS OVERVIEW                          [See All Tasks] │
│ ⭐ Urgent Tasks (with star icons)                          │
│ • [SP-001-25] Project Name - Task Details                  │
│ • [SP-002-25] Project Name - Task Details                  │
│ 🆕 New Tasks (with alert icons)                           │
│ • [SP-003-25] Project Name - Task Details                  │
│ • [SP-004-25] Project Name - Task Details                  │
│                                        [X MORE +]          │
├─────────────────────────────────────────────────────────────┤
│ 📁 YOUR PROJECTS                        [See All Projects] │
│ • [SP-001] Project Name (40% complete)                     │
│ • [SP-002] Project Name (60% complete)                     │
│ • [SP-003] Project Name (25% complete)                     │
│ • [SP-004] Project Name (80% complete)                     │
│ • [SP-005] Project Name (15% complete)                     │
└─────────────────────────────────────────────────────────────┘
```

**Dashboard Redirection Logic:**

```php
public function index(Request $request)
{
    $admin_access_level = AccessLevel::where('name', '=', 'Admin')->first();

    if (Auth::user()->role->name == 'SuperAdmin') {
        return redirect()->route('admin-dashboard');
    } elseif (Auth::user()->role->name == 'Admin') {
        return redirect()->route('admin-dashboard');
    } elseif ($admin_access_level && Auth::user()->access_level_id === $admin_access_level->id) {
        return redirect()->route('admin-dashboard');
    } elseif(Auth::user()->role->name == 'Client Member') {
        return redirect()->route('client.dashboard');
    } else {
        // Regular team member dashboard logic
        // Same task grouping logic as admin dashboard
        // Shows only assigned projects and tasks
    }
}
```

**Key Differences from Admin Dashboard:**

- **Fewer Quick Links:** Only essential links (Project List, Tasks List, Status Hub, PTO Calendar)
- **Limited Access:** Only shows projects and tasks assigned to the user
- **No Management Functions:** No user management, client creation, or enterprise management
- **Same Task Logic:** Uses identical task filtering (urgent and new tasks only)
- **Permission-Based Links:** "See All" links redirect to appropriate routes based on permissions

**Client Dashboard:**

**Route Structure:**

- **Route:** `/client/dashboard`
- **Controller:** `ClientController@dashboard`
- **Middleware:** Client Member role required
- **View:** `resources/views/client/dashboard.blade.php`

**Brand/Enterprise Dashboard:**

**Route Structure:**

- **Route:** `/brand/dashboard`
- **Controller:** `BrandController@dashboard`
- **Middleware:** `auth`, `isBrand`
- **View:** `resources/views/brand/dashboard.blade.php`

**Access Control Implementation:**

**admin_superadmin_permissions() Helper:**

```php
function admin_superadmin_permissions()
{
    $admin_access_level = \App\Models\AccessLevel::where('name', '=', 'Admin')->first();
    $admin_access_level_id = $admin_access_level ? $admin_access_level->id : null;

    return auth()->check() && (
        auth()->user()->hasRole(['SuperAdmin', 'Admin']) ||
        (isset(auth()->user()->access_level_id) &&
         auth()->user()->access_level_id === $admin_access_level_id)
    );
}
```

**SuperAdmin Middleware:**

```php
class SuperAdminPermissionMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();
        $roleName = $user->role?->name;
        $isSuperOrAdmin = in_array($roleName, ['SuperAdmin', 'Admin']);
        $adminAccessLevelId = AccessLevel::where('name', 'Admin')->value('id');

        if ($isSuperOrAdmin || $user->access_level_id === $adminAccessLevelId) {
            return $next($request);
        }

        abort(403, 'Access denied. Only Super Admins or Admins are allowed.');
    }
}
```

---

### Access Level Management

**Understanding Access Levels:**
Access levels provide an additional layer of permission control that can override role-based restrictions. This allows for flexible permission management without changing core roles.

**Access Level Types:**

- **Standard:** Default access level with role-based permissions
- **Admin:** Elevated access level that grants admin-like permissions to non-admin roles
- **Restricted:** Limited access level for temporary or probationary users

**Implementation Example:**

```php
// Team Member with Admin Access Level
if ($user->role->name === 'Team Member' && $user->access_level->name === 'Admin') {
    // Grant admin-level permissions while maintaining team member role
    return $this->grantAdminPermissions();
}
```

**Use Cases:**

- **Temporary Elevation:** Grant temporary admin access for specific projects
- **Cross-Training:** Allow team members to learn admin functions
- **Backup Administration:** Ensure continuity when primary admins are unavailable
- **Gradual Promotion:** Test users with elevated permissions before role change

---

## 🎯 **4. Core Features Deep Dive**

### 4.1 Project Management Lifecycle

**Project Creation Workflow:**

The project creation process in ShieldsGF Portal follows a structured approach that ensures all necessary information is captured and proper relationships are established.

**Prerequisites:**

- **Client Must Exist:** Every project requires an associated client
- **Brand Association (Optional):** Clients may have associated brands/enterprises
- **User Permissions:** Only Admins, SuperAdmins, or Team Members with Admin access can create projects

**Required Project Fields:**

```
┌─────────────────────────────────────────────────────────────┐
│ 📝 Project Creation Form                                    │
├─────────────────────────────────────────────────────────────┤
│ • Project Title: [Required, Unique]                        │
│ • Job Code: [Required, Format: SP-XXX-YY]                  │
│ • Client: [Required, Dropdown Selection]                   │
│ • Timeline: [Required, Predefined Options]                 │
│ • Team Members: [Required, Multi-select]                   │
│ • Invoice Schedule: [Required, Billing Frequency]          │
│ • Harvest Link: [Optional, Time Tracking Integration]      │
│ • Social Details: [Required, Array of Social Media Links]  │
│ • Kickoff Meeting: [Optional, Yes/No with Details]         │
└─────────────────────────────────────────────────────────────┘
```

**Project Phases System:**

Projects in ShieldsGF Portal follow a standardized phase structure that provides clear milestones and progress tracking.

**Standard Phase Progression:**

1. **📋 Design Phase**

   - Initial planning and wireframing
   - Client requirements gathering
   - Design mockups and prototypes
   - Design approval and finalization

2. **💻 Development Phase**

   - Code implementation
   - Feature development
   - Integration and testing
   - Code review and optimization

3. **🚀 Deploy Phase**

   - Testing and quality assurance
   - Staging environment setup
   - Production deployment
   - Launch coordination

4. **🔧 Manage Phase**

   - Post-launch monitoring
   - Bug fixes and optimizations
   - Performance monitoring
   - Client training and handover

5. **⭐ Voyager Phase**
   - Long-term maintenance
   - Ongoing support
   - Feature enhancements
   - Performance optimization

**Phase Management Features:**

- **Category-Based Organization:** Projects within phases are organized by categories
- **Target Date Tracking:** Each phase has specific completion targets
- **Progress Visualization:** Visual indicators show phase completion percentages
- **Automatic Transitions:** Projects can be moved between phases via drag-and-drop
- **Completion Calculation:** Project completion determined by highest phase ID target date

**Project Information Display:**

**Job Code Format:**

- **Structure:** [CLIENT-PROJECT-YEAR] (e.g., SP-003-25)
- **Purpose:** Unique identification and easy reference
- **Usage:** Displayed in all project-related interfaces

**Phase Indicators:**

- **Format:** "PHASE X/Y" (e.g., "PHASE 2/5")
- **Visual Elements:** Progress bars and percentage completion
- **Color Coding:** Different colors for different phase types

**Team Visualization:**

- **Color-Coded Flags:** Each team member has a unique color
- **Team Member Indicators:** Visual flags show assigned team members
- **Role Identification:** Different indicators for different roles

### 4.2 Task Management System

**Task Categories and Workflow:**

The task management system provides comprehensive tracking and organization capabilities with role-based access control.

**Task Status Categories:**

```
🔴 Urgent Tasks
├── High priority items requiring immediate attention
├── Displayed with star icons for visual prominence
├── Appear at top of task lists
└── Generate automatic notifications

🆕 New Tasks
├── Recently created tasks awaiting assignment
├── Special visual indicators for identification
├── Require initial review and prioritization
└── Auto-assigned based on project team

📝 Regular Tasks
├── Standard workflow tasks
├── Simple display without special icons
├── Follow normal priority and due date rules
└── Standard notification schedule

💬 Feedback Tasks
├── Tasks requiring client or stakeholder input
├── Special workflow for external communication
├── Tracking of feedback receipt and implementation
└── Integration with messaging system

⚡ In Progress Tasks
├── Currently active tasks being worked on
├── Time tracking integration
├── Progress percentage tracking
└── Regular status update requirements

✅ Completed Tasks
├── Finished tasks awaiting final review
├── Archive after confirmation
├── Performance metrics tracking
└── Client notification if required

🎯 Recently Finished Tasks
├── Completed tasks within recent timeframe
├── Visible only with specific filter applied
├── Used for performance reporting
└── Hidden in general task views
```

**Task Creation and Assignment:**

**Mandatory Fields:**

- **Task Name:** Descriptive title for the task
- **Description:** Detailed task requirements and specifications
- **Project Assignment:** Must be associated with an existing project
- **Status:** Initial status (typically "New")

**Optional Fields:**

- **Due Date:** Target completion date
- **Priority Level:** Urgent, High, Medium, Low
- **Estimated Duration:** Time estimate for completion
- **Category:** Task categorization for organization
- **Phase:** Associated project phase

**Assignment Process:**

- **Multi-User Assignment:** Tasks can be assigned to multiple team members
- **Role-Based Assignment:** Consider user roles and capabilities
- **Workload Balancing:** System suggests assignments based on current workload
- **Notification System:** Automatic notifications to assigned users

**Rich Text Comment System:**

The comment system provides advanced formatting and collaboration features:

**Formatting Options:**

- **Bold Text:** Ctrl+B or button toggle
- **Italic Text:** Ctrl+I or button toggle
- **Underline Text:** Ctrl+U or button toggle
- **Line Breaks:** Shift+Enter for multi-line comments
- **Visual Feedback:** Selected text highlighted when formatting applied

**User Mention System:**

- **Trigger:** @ symbol followed by user name
- **Dropdown Suggestions:** Real-time user search and selection
- **Smart Replacement:** Selected mention replaces partial typed name
- **Notification Integration:** Mentioned users receive notifications

**Advanced Features:**

- **Image Support:** Paste images directly into comments
- **Link Detection:** Automatic link formatting and highlighting
- **Link Behavior:** All links open in new windows (target='\_blank')
- **Threading:** Nested comment replies for organized discussions
- **Timezone Handling:** Timestamps display correctly for user's timezone

### 4.3 Status Hub - Visual Project Tracking

**Overview:**
The Status Hub is the central command center for visual project management, providing a comprehensive overview of all projects across different phases with intuitive drag-and-drop functionality.

**Core Functionality:**

**Visual Project Representation:**

```
┌─────────────────────────────────────────────────────────────┐
│ 📊 Status Hub - Project Overview                           │
├─────────────────────────────────────────────────────────────┤
│ 📋 Design    │ 💻 Development │ 🚀 Deploy    │ 🔧 Manage   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────┐  │ ┌─────────┐    │ ┌─────────┐  │ ┌─────────┐ │
│ │[SP-001] │  │ │[SP-003] │    │ │[SP-005] │  │ │[SP-007] │ │
│ │Project A│  │ │Project C│    │ │Project E│  │ │Project G│ │
│ │●●●○○ 60%│  │ │●●●●○ 80%│    │ │●●●●● 100%│ │ │●●●●● 100%│ │
│ │👤👤👤   │  │ │👤👤     │    │ │👤👤👤   │  │ │👤👤     │ │
│ └─────────┘  │ └─────────┘    │ └─────────┘  │ └─────────┘ │
│              │                │              │             │
│ ┌─────────┐  │ ┌─────────┐    │              │ ┌─────────┐ │
│ │[SP-002] │  │ │[SP-004] │    │              │ │[SP-008] │ │
│ │Project B│  │ │Project D│    │              │ │Project H│ │
│ │●●○○○ 40%│  │ │●●●○○ 60%│    │              │ │●●●●● 100%│ │
│ │👤👤     │  │ │👤👤👤   │    │              │ │👤       │ │
│ └─────────┘  │ └─────────┘    │              │ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**Project Box Components:**

**Progress Indicators:**

- **Circular Progress Bars:** Visual percentage completion
- **Color-Coded Phases:** Different colors for each phase type
- **Percentage Display:** Numerical progress indication
- **Phase Completion Status:** Visual indicators for phase milestones

**Project Information Display:**

- **Job Code:** Unique project identifier (e.g., [SP-003-25])
- **Project Name:** Descriptive project title
- **Client Information:** Associated client details
- **Phase Indicator:** Current phase and total phases (e.g., "PHASE 2/5")

**Team Member Visualization:**

- **Color-Coded Flags:** Each team member represented by unique color
- **Team Size Indicator:** Visual representation of team size
- **Role Identification:** Different visual indicators for different roles
- **Hover Details:** Team member names and roles on hover

**Interactive Features:**

**Drag-and-Drop Functionality:**

- **Phase Transitions:** Drag projects between phase columns
- **Real-Time Updates:** Immediate visual feedback during drag operations
- **Validation:** System prevents invalid phase transitions
- **Auto-Save:** Changes saved automatically upon drop
- **Notification System:** Team notifications for phase changes

**Project Actions:**

- **Click to View:** Click project boxes for detailed project view
- **Archive/Unarchive:** Toggle project visibility
- **Edit Project:** Direct access to project editing
- **Team Management:** Quick team member assignment

**Category-Based Organization:**

**Phase Categories:**

- **Design Categories:** UI/UX, Wireframing, Prototyping
- **Development Categories:** Frontend, Backend, Integration
- **Deploy Categories:** Testing, Staging, Production
- **Manage Categories:** Maintenance, Support, Optimization

**Category Display:**

- **Category Names:** Displayed instead of generic phase names
- **Color Coding:** Consistent color scheme across categories
- **Progress Tracking:** Category-specific progress indicators
- **Filtering Options:** Filter by category or phase type

**Archive Management:**

**Archive Functionality:**

- **Hide Completed Projects:** Remove clutter from active view
- **Restore Projects:** Bring archived projects back to active status
- **Archive History:** Track when projects were archived
- **Bulk Operations:** Archive multiple projects simultaneously

**Archive Indicators:**

- **Visual Distinction:** Archived projects shown with different styling
- **Status Labels:** Clear indication of archived status
- **Restore Options:** Easy restoration process
- **Permission Control:** Only authorized users can archive/restore

### 4.4 PTO Calendar & Holiday Management

**System Overview:**
The PTO Calendar system provides comprehensive holiday and time-off management with role-based permissions and regional customization.

**Admin Capabilities:**

**Global Holiday Management:**

- **Company-Wide Holidays:** Add holidays that apply to all users
- **Regional Settings:** Choose between Indian holidays, US holidays, or custom
- **Holiday Types:** National holidays, company events, office closures
- **Bulk Import:** Import standard holiday calendars
- **Recurring Events:** Set up annual recurring holidays

**Personal Leave Administration:**

- **Add Personal Leave:** Admins can add PTO for individual team members
- **Leave Approval:** Approve or deny PTO requests
- **Leave Balance Tracking:** Monitor team member PTO balances
- **Leave Policies:** Configure company leave policies
- **Reporting:** Generate leave reports and analytics

**Team PTO Oversight:**

- **Team Calendar View:** See all team member PTO in one view
- **Conflict Detection:** Identify scheduling conflicts
- **Coverage Planning:** Ensure adequate team coverage
- **Notification Management:** Configure PTO notification settings

**User Capabilities:**

**Personal PTO Management:**

- **PTO Requests:** Submit time-off requests for approval
- **Personal Calendar:** View personal PTO and holidays
- **Balance Tracking:** Monitor available PTO balance
- **Request History:** View past PTO requests and approvals

**Team Visibility:**

- **Team Calendar:** View team member PTO (with privacy settings)
- **Project Impact:** See how PTO affects project timelines
- **Coverage Information:** Identify who's covering responsibilities
- **Holiday Awareness:** Stay informed about company holidays

**Calendar Features:**

**Interactive Calendar Interface:**

```
┌─────────────────────────────────────────────────────────────┐
│ 📅 PTO Calendar - December 2024                            │
├─────────────────────────────────────────────────────────────┤
│ Sun │ Mon │ Tue │ Wed │ Thu │ Fri │ Sat                     │
├─────────────────────────────────────────────────────────────┤
│  1  │  2  │  3  │  4  │  5  │  6  │  7                      │
│     │     │     │     │     │     │                         │
├─────────────────────────────────────────────────────────────┤
│  8  │  9  │ 10  │ 11  │ 12  │ 13  │ 14                     │
│     │     │     │     │     │     │                         │
├─────────────────────────────────────────────────────────────┤
│ 15  │ 16  │ 17  │ 18  │ 19  │ 20  │ 21                     │
│     │     │     │     │     │     │                         │
├─────────────────────────────────────────────────────────────┤
│ 22  │ 23  │ 24  │ 25  │ 26  │ 27  │ 28                     │
│     │     │     │🎄   │🏖️   │     │                         │
│     │     │     │Xmas │John │     │                         │
│     │     │     │     │Out  │     │                         │
├─────────────────────────────────────────────────────────────┤
│ 29  │ 30  │ 31  │     │     │     │                         │
│     │     │🎊   │     │     │     │                         │
│     │     │NYE  │     │     │     │                         │
└─────────────────────────────────────────────────────────────┘
```

**Event Types and Display:**

- **🎄 Company Holidays:** Global holidays (Christmas, New Year, etc.)
- **🏖️ Personal PTO:** Individual time off ("John is out")
- **📅 Project Milestones:** Project completion dates (blue)
- **🎯 Phase Deadlines:** Phase completion dates (color-coded)

**AJAX-Based Operations:**

- **Real-Time Updates:** Add events without page refresh
- **Instant Feedback:** Immediate visual confirmation
- **Error Handling:** Graceful error messages for conflicts
- **Auto-Save:** Changes saved automatically

**Integration Features:**

**Resource Allocation Integration:**

- **PTO Impact:** Automatically factors PTO into resource calculations
- **Availability Tracking:** Updates team member availability
- **Project Timeline Adjustment:** Adjusts project timelines for PTO
- **Workload Redistribution:** Suggests workload adjustments

**Project Timeline Integration:**

- **Milestone Visibility:** Project completion dates shown in blue
- **Phase Deadlines:** Phase completion dates with color coding
- **Conflict Alerts:** Warnings for PTO conflicts with deadlines
- **Timeline Adjustments:** Automatic timeline adjustments for holidays

### 4.5 Resource Allocation & Planning

**System Overview:**
The Resource Allocation system provides sophisticated workforce planning and utilization tracking, enabling optimal resource distribution across projects and phases.

**Calculation Methodology:**

**Work Week Standards:**

- **Standard Work Week:** 32 hours (6+ hours per day)
- **Percentage Base:** Calculations based on 40-hour work week for standardization
- **Flexibility:** Can accommodate different work schedules and arrangements
- **Overtime Tracking:** Utilization can exceed 100% when workload is high

**Multi-Role Hour Allocation:**

```
┌─────────────────────────────────────────────────────────────┐
│ 👤 Team Member: John Doe                                   │
├─────────────────────────────────────────────────────────────┤
│ Role                │ Weekly Hours │ Hourly Rate │ Total    │
├─────────────────────────────────────────────────────────────┤
│ 💻 Developer        │ 25 hours     │ $50/hr      │ $1,250   │
│ 🎨 Designer         │ 10 hours     │ $45/hr      │ $450     │
│ 📋 Project Manager  │ 5 hours      │ $60/hr      │ $300     │
├─────────────────────────────────────────────────────────────┤
│ Total Weekly Hours  │ 40 hours     │ Avg: $50/hr │ $2,000   │
└─────────────────────────────────────────────────────────────┘
```

**Role-Based Allocation Rules:**

**Developer Hours:**

- **Source:** Resources table per user
- **Allocation:** Project-specific hour assignments
- **Tracking:** Weekly utilization monitoring
- **Flexibility:** Can be adjusted based on project needs

**Designer Hours:**

- **Source:** Resources table per user
- **Phase Focus:** Primarily during design phase
- **Cross-Project:** Can work on multiple projects simultaneously
- **Specialization:** Different design specialties tracked separately

**Project Manager Hours:**

- **Allocation:** 20-30% of time during active phases
- **Phase Distribution:** Design (30%), Code (25%), Deploy (30%), Manage (20%)
- **Oversight:** Multiple project management simultaneously
- **Escalation:** Additional hours for complex projects

**Customer Success Hours:**

- **Source:** Resources table tracking
- **Focus:** Post-launch and maintenance phases
- **Client Interaction:** Direct client communication and support
- **Reporting:** Performance metrics and client satisfaction

**Visual Display System:**

**Calendar-Based View:**

```
┌─────────────────────────────────────────────────────────────┐
│ 📊 Resource Allocation - Week of Dec 16-22, 2024           │
├─────────────────────────────────────────────────────────────┤
│ Team Member    │ Mon │ Tue │ Wed │ Thu │ Fri │ Weekly Total │
├─────────────────────────────────────────────────────────────┤
│ John Doe       │ 85% │ 90% │ 75% │ 95% │ 80% │ 85% (34/40) │
│ • SP-001 (15h) │     │     │     │     │     │              │
│ • SP-003 (12h) │     │     │     │     │     │              │
│ • SP-005 (7h)  │     │     │     │     │     │              │
├─────────────────────────────────────────────────────────────┤
│ Jane Smith     │ 70% │ 80% │ 85% │ 75% │ 90% │ 80% (32/40) │
│ • SP-002 (20h) │     │     │     │     │     │              │
│ • SP-004 (12h) │     │     │     │     │     │              │
├─────────────────────────────────────────────────────────────┤
│ 🏖️ Mike Johnson│ PTO │ PTO │ 60% │ 70% │ 65% │ 39% (12/32) │
│ • SP-006 (12h) │     │     │     │     │     │              │
└─────────────────────────────────────────────────────────────┘
```

**Hour Display Format:**

- **Percentage:** Visual utilization percentage
- **Actual Hours:** "(allocated/available) hours" format
- **Project Breakdown:** Individual project hour allocations
- **PTO Integration:** Automatic adjustment for time off
- **Overtime Indication:** Visual alerts for over-allocation

**Advanced Calculation Features:**

**Remaining Project Hours:**

- **Phase Tracking:** Monitor hours used in each project phase
- **Burn Rate:** Calculate weekly hour consumption
- **Projection:** Estimate completion dates based on current pace
- **Adjustment:** Automatic recalculation when scope changes

**Phase Transition Tracking:**

- **Resource Movement:** Track how resources move between teams
- **Phase Completion Impact:** Adjust allocations when phases complete
- **Skill Requirements:** Match resource skills to phase needs
- **Continuity Planning:** Ensure smooth transitions between phases

**Extended Timeline Planning:**

- **Beyond Current Month:** Show weeks until all project phases complete
- **Long-term Planning:** Resource needs for upcoming quarters
- **Capacity Planning:** Identify future resource constraints
- **Hiring Recommendations:** Suggest when additional resources needed

**Integration Features:**

**PTO and Holiday Integration:**

- **Automatic Adjustment:** Factor in approved PTO and holidays
- **Availability Calculation:** Real-time availability updates
- **Coverage Planning:** Identify coverage needs during absences
- **Holiday Impact:** Regional holiday considerations

**Project Timeline Integration:**

- **Milestone Alignment:** Align resource allocation with project milestones
- **Deadline Pressure:** Increase allocation for approaching deadlines
- **Phase Dependencies:** Ensure adequate resources for dependent phases
- **Client Expectations:** Balance resource allocation with client commitments

**Reporting and Analytics:**

**Utilization Reports:**

- **Individual Performance:** Personal utilization trends
- **Team Performance:** Department-level utilization
- **Project Efficiency:** Resource efficiency by project
- **Trend Analysis:** Historical utilization patterns

**Capacity Planning:**

- **Future Needs:** Projected resource requirements
- **Skill Gap Analysis:** Identify skill shortages
- **Training Recommendations:** Suggest skill development areas
- **Hiring Forecasts:** Predict when new hires needed

### 4.6 Team Collaboration Tools

**Communication Hub:**
The team collaboration system provides multiple channels for effective communication and coordination across projects and teams.

**Project Messaging System:**

**Thread-Based Conversations:**

- **Subject Organization:** All messages require descriptive subjects
- **Threaded Replies:** Nested conversation structure
- **File Attachments:** Support for multiple file types (20MB max per file)
- **Email Integration:** Optional email notifications for message recipients

**Message Creation Process:**

```
┌─────────────────────────────────────────────────────────────┐
│ 💬 New Project Message                                      │
├─────────────────────────────────────────────────────────────┤
│ Project: [SP-003-25] Sample Project 3                      │
│ Subject: [Required] ________________________________        │
│ Message: [Rich Text Editor]                                 │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Type your message here...                               │ │
│ │ Supports @mentions, formatting, and links              │ │
│ └─────────────────────────────────────────────────────────┘ │
│ Files: [Choose Files] [Drag & Drop Support]                │
│ Recipients: ☑ All Team Members ☐ Specific Users            │
│ Email Notification: ☑ Send email along with message        │
│ [Send Message] [Save Draft] [Cancel]                       │
└─────────────────────────────────────────────────────────────┘
```

**Advanced Messaging Features:**

- **Team Filtering:** Messages filtered by team regions (US team focus)
- **Priority Levels:** Mark messages as urgent or normal priority
- **Read Receipts:** Track message read status
- **Search Functionality:** Full-text search across all messages
- **Archive System:** Archive old conversations for reference

**File Sharing and Management:**

**Upload Capabilities:**

- **Multiple File Support:** Select and upload multiple files simultaneously
- **Drag-and-Drop Interface:** Intuitive file upload experience
- **File Type Validation:** Automatic validation of file types and sizes
- **Progress Tracking:** Real-time upload progress indicators

**File Organization:**

- **Project-Based Storage:** Files organized by project structure
- **Version Control:** Track file versions and changes
- **Access Control:** Role-based file access permissions
- **Download Tracking:** Log file downloads for audit purposes

**Collaboration Features:**

**Real-Time Updates:**

- **Live Notifications:** Instant notifications for new messages and files
- **Status Indicators:** Show when team members are online
- **Typing Indicators:** See when someone is composing a message
- **Auto-Refresh:** Automatic content updates without page refresh

**Team Coordination:**

- **@Mention System:** Tag team members in messages and comments
- **Notification Preferences:** Customize notification settings per user
- **Team Presence:** See who's currently active in the system
- **Quick Actions:** Fast access to common collaboration tasks

### 4.7 Real-Time Communication with Laravel Reverb

**WebSocket Implementation:**
ShieldsGF Portal uses Laravel Reverb as its WebSocket server for real-time communication, providing instant updates and live interactions across the platform.

**Reverb Configuration:**

```javascript
// Echo.js Configuration
window.Echo = new Echo({
  broadcaster: "reverb",
  key: import.meta.env.VITE_REVERB_APP_KEY,
  wsHost: import.meta.env.VITE_REVERB_HOST,
  wsPort: import.meta.env.VITE_REVERB_PORT ?? 80,
  wssPort: import.meta.env.VITE_REVERB_PORT ?? 443,
  forceTLS: (import.meta.env.VITE_REVERB_SCHEME ?? "https") === "https",
  enabledTransports: ["ws", "wss"],
});
```

**Broadcasting Channels:**

- **Private User Channels:** `App.Models.User.{id}` - Personal notifications
- **Comments Channel:** `comments` - General comment updates
- **Task Comments:** `task-comments.{task_id}` - Task-specific comment updates

**Real-Time Features:**

**Live Comment System:**

- **Instant Updates:** Comments appear immediately for all users viewing the task
- **User Information:** Real-time display of commenter name and profile image
- **Attachment Support:** Live updates for file attachments in comments
- **Timezone Handling:** Consistent timestamp display across different timezones
- **Notification Integration:** Automatic email notifications for mentioned users

**Event Broadcasting:**

```php
// CommentAdded Event
class CommentAdded implements ShouldBroadcastNow
{
    public function broadcastOn()
    {
        return [
            new PrivateChannel('task-comments.' . $this->task_id),
        ];
    }

    public function broadcastWith()
    {
        return [
            'comment' => [
                'id' => $this->comment->id,
                'comment_body' => $this->comment->comment_body,
                'created_at' => $this->comment->created_at->toISOString(),
                'formatted_time' => $this->comment->created_at->format('M d, Y h:i A'),
                'commentAttachments' => $attachments
            ],
            'user' => $this->user->name,
            'user_id' => $this->user->id,
            'user_profile_image' => $this->user_profile_image,
            'task_id' => $this->task_id
        ];
    }
}
```

**Client-Side Integration:**

```javascript
// Real-time comment listening
window.Echo.private('task-comments.{{ task_id }}')
    .listen('CommentAdded', (e) => {
        // Create new comment element
        const isCurrentUser = parseInt(e.user_id) === parseInt({{ Auth::id() }});
        const commentRow = $('<div class="comment_row" data-comment_id="' + e.comment.id + '"></div>');

        // Add comment to DOM with proper formatting
        // Handle file attachments and user mentions
        // Update comment count and scroll to new comment
    });
```

### 4.8 Advanced UI/UX Features

**Interactive Cursor Effects:**

- **Custom Cursor:** Animated cursor effects for desktop users (hidden on mobile)
- **Hover Animations:** Special cursor animations for links and buttons
- **Smooth Tracking:** Dual-cursor system with delayed following effect

**Confetti Animations:**

- **Scroll-Triggered:** Confetti animation when users reach page bottom
- **Celebration Effects:** Visual feedback for completed actions
- **Configurable Particles:** Customizable particle count and spread patterns

**Color Picker System:**

- **Dual Input:** Text input and color picker synchronized
- **Real-Time Sync:** Instant synchronization between text and visual picker
- **User Color Codes:** Team member identification through color coding
- **Validation:** Automatic color format validation

**Drag-and-Drop Functionality:**

**File Upload Drag-and-Drop:**

- **Visual Feedback:** Overlay appears when files are dragged over drop zones
- **Multiple File Support:** Handle multiple files simultaneously
- **File Type Validation:** Automatic validation of file types and sizes
- **Progress Indicators:** Real-time upload progress tracking
- **Error Handling:** Graceful error messages for failed uploads

**Project Status Drag-and-Drop:**

- **Visual Placeholders:** Drop placeholders show where projects will be placed
- **Phase Transitions:** Drag projects between different phases
- **Real-Time Updates:** Immediate backend updates via AJAX
- **Validation:** Prevent invalid phase transitions
- **Animation Effects:** Smooth drag animations with opacity changes

**Loading and Animation System:**

- **Page Loading:** Smooth loading animations with fade effects
- **Livewire Loading:** Loading states for Livewire component updates
- **Skeleton Loading:** Placeholder content during data loading
- **Micro-Interactions:** Subtle animations for better user experience

### 4.9 File Management and Media Handling

**Advanced File Upload System:**

- **Drag-and-Drop Interface:** Intuitive file upload with visual feedback
- **Multiple File Selection:** Batch file uploads with progress tracking
- **File Type Detection:** Automatic file type identification and validation
- **Size Validation:** Configurable file size limits (20MB default)
- **Preview Generation:** Automatic preview generation for supported file types

**Image Handling:**

- **Format Support:** JPG, JPEG, PNG, GIF, BMP, WebP, SVG
- **Automatic Optimization:** Image compression and optimization
- **Responsive Display:** Adaptive image display across devices
- **Fallback System:** Default images for missing or broken files
- **Storage Flexibility:** Support for local storage, S3, and other cloud providers

**File Organization:**

- **Project-Based Structure:** Files organized by project hierarchy
- **Version Control:** Track file versions and changes
- **Access Control:** Role-based file access permissions
- **Search Functionality:** Full-text search across file names and metadata
- **Bulk Operations:** Mass file operations for efficiency

---

## � **5. Administrative Functions**

### 5.1 Advanced Livewire Components

**Component Architecture:**
ShieldsGF Portal leverages Livewire 3.6+ for reactive, server-side rendered components that provide seamless user interactions without page refreshes.

**Key Livewire Components:**

**ClientList Component:**

- **Pagination:** Bootstrap-themed pagination with query string persistence
- **Filtering:** Year-based and alphabetical filtering with URL state management
- **Sorting:** Multi-column sorting with direction indicators
- **Search:** Real-time search with debouncing
- **Loading States:** Visual loading indicators during operations

```php
class ClientList extends Component
{
    use WithPagination;

    public $year = 'all';
    public $alphabet = null;
    public $sortColumn = 'name';
    public $sortDirection = 'asc';

    protected $queryString = [
        'year' => ['except' => 'all'],
        'alphabet' => ['except' => null],
        'sortColumn' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
    ];

    public function updatingAlphabet()
    {
        $this->resetPage();
        usleep(300000); // 300ms delay for loading effect
    }
}
```

**ProjectTasks Component:**

- **Dynamic Loading:** Load more functionality for large task lists
- **Status Filtering:** Filter tasks by status with URL persistence
- **Sorting Options:** Flexible sorting with dropdown interface
- **Urgent Task Handling:** Special handling for urgent and new tasks

**TaskOverview Component:**

- **Advanced Filtering:** Multi-criteria filtering (status, year, month, alphabet)
- **Search Integration:** Full-text search across task names and descriptions
- **Pagination Control:** Efficient pagination for large datasets
- **State Management:** Persistent filter states across sessions

### 5.2 PDF Generation and Reporting

**Spatie Laravel PDF Integration:**
The system uses Spatie Laravel PDF for generating comprehensive reports and documentation.

**Report Generation Features:**

- **Metrics Reports:** Automated generation of project metrics and analytics
- **Custom Templates:** Blade-based PDF templates with full styling control
- **File Storage:** Automatic storage of generated reports with database tracking
- **Download Management:** Secure file downloads with access logging

```php
public function generatePdf($metric, $contentToPrint)
{
    return Pdf::view('admin.reports.metrics_report', $data)
        ->format('a4')
        ->margins(15, 15, 15, 15);
}
```

**Report Types:**

- **Project Performance Reports:** Comprehensive project analytics
- **Resource Utilization Reports:** Team performance and allocation metrics
- **Time Tracking Reports:** Detailed time analysis and billing reports
- **Client Reports:** Client-specific project summaries and progress reports

### 5.3 Permission System with Spatie Laravel Permission

**Advanced Permission Management:**
The system implements a sophisticated permission system using Spatie Laravel Permission package for granular access control.

**Permission Structure:**

- **Role-Based Permissions:** Permissions assigned to roles, roles assigned to users
- **Direct Permissions:** Individual permissions can be assigned directly to users
- **Permission Inheritance:** Users inherit permissions from their roles
- **Dynamic Permission Checking:** Real-time permission validation

**Custom Middleware Implementation:**

```php
class PermissionMiddleware
{
    public function handle(Request $request, Closure $next, string $permission)
    {
        $role = Role::with("permissions")->where('id', '=', Auth::user()->role_id)->first();
        if ($role->permissions->isNotEmpty()) {
            if($role->permissions->contains('name', $permission)){
                return $next($request);
            }
        }
        abort(403, 'OPERATION/ACCESS NOT ALLOWED');
    }
}
```

**Middleware Types:**

- **PermissionMiddleware:** General permission checking
- **SuperAdminPermissionMiddleware:** Super admin specific access control
- **TaskPermissionMiddleware:** Task-related permission validation
- **isBrand:** Brand-specific access control

### 5.4 Queue System and Background Processing

**Queue Configuration:**
The system supports multiple queue drivers for background job processing:

**Supported Queue Drivers:**

- **Database Queue:** Default queue driver using database tables
- **Redis Queue:** High-performance Redis-based queue system
- **Sync Queue:** Synchronous processing for development
- **SQS Queue:** Amazon SQS integration for cloud deployments

**Background Jobs:**

- **Email Notifications:** Asynchronous email sending for better performance
- **File Processing:** Background file uploads and processing
- **Report Generation:** Asynchronous PDF generation
- **Data Synchronization:** Background data sync operations

### 5.5 Environment Configuration and Deployment

**Comprehensive Environment Setup:**

**Core Application Settings:**

```env
# Application Configuration
APP_NAME="ShieldsGF Portal"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com
APP_KEY=base64:generated-key

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sgf_portal
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

**Reverb WebSocket Configuration:**

```env
# Reverb WebSocket Server
REVERB_APP_ID=your-app-id
REVERB_APP_KEY=your-app-key
REVERB_APP_SECRET=your-app-secret
REVERB_HOST=your-domain.com
REVERB_PORT=443
REVERB_SCHEME=https

# Broadcasting
BROADCAST_CONNECTION=reverb
VITE_REVERB_APP_KEY="${REVERB_APP_KEY}"
VITE_REVERB_HOST="${REVERB_HOST}"
VITE_REVERB_PORT="${REVERB_PORT}"
VITE_REVERB_SCHEME="${REVERB_SCHEME}"
```

**Mail and Notification Configuration:**

```env
# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"
```

**File Storage Configuration:**

```env
# File Storage
FILESYSTEM_DISK=public
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your-s3-bucket
```

**Cache and Session Configuration:**

```env
# Cache Configuration
CACHE_STORE=file
SESSION_DRIVER=file
SESSION_LIFETIME=120

# Queue Configuration
QUEUE_CONNECTION=database
```

### 5.6 Asset Management with Vite

**Modern Asset Pipeline:**
The system uses Vite for fast, modern asset compilation and hot module replacement during development.

**Vite Configuration:**

```javascript
import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";

export default defineConfig({
  plugins: [laravel(["resources/css/app.css", "resources/js/app.js"])],
});
```

**Asset Features:**

- **Hot Module Replacement:** Instant updates during development
- **Code Splitting:** Optimized bundle splitting for better performance
- **CSS Processing:** Advanced CSS processing with PostCSS
- **JavaScript Bundling:** Modern JavaScript bundling with tree shaking
- **Image Optimization:** Automatic image optimization and compression

**Development vs Production:**

- **Development:** `npm run dev` for development server with HMR
- **Production:** `npm run build` for optimized production builds
- **Asset Versioning:** Automatic asset versioning for cache busting

---

## �📋 **Data Structure for JavaScript Implementation**

When converting this documentation to HTML/CSS/JS, the data should be structured as follows:

```javascript
const docsData = {
  // 1. Introduction & Overview
  overview: {
    title: "ShieldsGF Portal - Complete Documentation",
    description:
      "Comprehensive project management and team collaboration platform",
    sections: {
      introduction: {
        title: "What is ShieldsGF Portal?",
        content: `
          <p>ShieldsGF Portal is a comprehensive project management and team collaboration platform designed to bridge the gap between clients and development teams...</p>
          <h3>Core Purpose:</h3>
          <ul>
            <li>Streamline project workflows from inception to completion</li>
            <li>Provide transparent project visibility for all stakeholders</li>
            <li>Optimize resource allocation and team utilization</li>
            <li>Facilitate effective communication between teams and clients</li>
            <li>Maintain comprehensive project documentation and history</li>
          </ul>
        `,
        subsections: ["core-purpose", "key-benefits", "target-users"],
      },
      keyFeatures: {
        title: "Key Features & Benefits",
        content: `
          <div class="feature-grid">
            <div class="feature-card">
              <h4>🎯 Project Management Excellence</h4>
              <ul>
                <li>Complete project lifecycle management with phase tracking</li>
                <li>Visual project status representation through Status Hub</li>
                <li>Drag-and-drop project phase transitions</li>
                <li>Real-time progress monitoring and reporting</li>
                <li>Client-specific project portals for transparency</li>
              </ul>
            </div>
            <!-- Additional feature cards... -->
          </div>
        `,
        subsections: [
          "project-management",
          "team-collaboration",
          "resource-planning",
          "admin-control",
        ],
      },
      systemArchitecture: {
        title: "System Architecture Overview",
        content: `
          <h3>Technology Stack:</h3>
          <div class="tech-stack">
            <div class="tech-item">
              <strong>Backend:</strong> Laravel 11.x (PHP 8.1+)
            </div>
            <div class="tech-item">
              <strong>Frontend:</strong> Livewire for reactive components
            </div>
            <div class="tech-item">
              <strong>Database:</strong> MySQL 8.0+ with optimized relationships
            </div>
            <!-- Additional tech items... -->
          </div>
        `,
        subsections: [
          "technology-stack",
          "architecture-principles",
          "scalability",
        ],
      },
      userRoles: {
        title: "User Roles & Permissions Matrix",
        content: `
          <div class="roles-matrix">
            <table class="permissions-table">
              <thead>
                <tr>
                  <th>Role</th>
                  <th>Project Management</th>
                  <th>User Management</th>
                  <th>System Config</th>
                  <th>Resource Planning</th>
                  <th>Client Communication</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><strong>SuperAdmin</strong></td>
                  <td>✅ Full Access</td>
                  <td>✅ Full Access</td>
                  <td>✅ Full Access</td>
                  <td>✅ Full Access</td>
                  <td>✅ Full Access</td>
                </tr>
                <!-- Additional role rows... -->
              </tbody>
            </table>
          </div>
        `,
        subsections: ["role-hierarchy", "permission-matrix", "access-levels"],
      },
    },
  },

  // 2. Getting Started
  gettingStarted: {
    title: "Getting Started Guide",
    description: "Complete setup and installation guide",
    sections: {
      systemRequirements: {
        title: "System Requirements",
        content: `
          <div class="requirements-grid">
            <div class="requirement-category">
              <h4>Server Requirements:</h4>
              <ul>
                <li><strong>PHP:</strong> 8.1 or higher with required extensions</li>
                <li><strong>Web Server:</strong> Apache 2.4+ or Nginx 1.18+</li>
                <li><strong>Database:</strong> MySQL 8.0+ or MariaDB 10.4+</li>
                <li><strong>Memory:</strong> Minimum 512MB RAM (2GB+ recommended)</li>
                <li><strong>Storage:</strong> 1GB+ available disk space</li>
              </ul>
            </div>
            <!-- Additional requirement categories... -->
          </div>
        `,
        subsections: [
          "server-requirements",
          "development-environment",
          "php-extensions",
        ],
      },
      installation: {
        title: "Installation Guide",
        content: `
          <div class="installation-steps">
            <div class="step">
              <h4>Step 1: Clone Repository</h4>
              <pre><code>git clone [repository-url] sgf-portal
cd sgf-portal</code></pre>
            </div>
            <div class="step">
              <h4>Step 2: Install Dependencies</h4>
              <pre><code># Install PHP dependencies
composer install

# Install Node.js dependencies
npm install</code></pre>
            </div>
            <!-- Additional installation steps... -->
          </div>
        `,
        subsections: [
          "clone-repository",
          "install-dependencies",
          "environment-setup",
          "database-setup",
          "asset-compilation",
          "storage-configuration",
        ],
      },
      configuration: {
        title: "Initial Configuration",
        content: `
          <div class="config-sections">
            <div class="config-section">
              <h4>Environment Variables:</h4>
              <pre><code># Application
APP_NAME="ShieldsGF Portal"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sgf_portal
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password</code></pre>
            </div>
            <!-- Additional configuration sections... -->
          </div>
        `,
        subsections: [
          "environment-variables",
          "web-server-config",
          "ssl-setup",
        ],
      },
      firstLogin: {
        title: "First Login & Setup",
        content: `
          <div class="setup-guide">
            <div class="default-account">
              <h4>Default Admin Account:</h4>
              <p>After running the database seeder, a default SuperAdmin account is created:</p>
              <ul>
                <li><strong>Email:</strong> <EMAIL></li>
                <li><strong>Password:</strong> admin123 (change immediately after first login)</li>
              </ul>
            </div>
            <div class="setup-checklist">
              <h4>Initial Setup Steps:</h4>
              <ol>
                <li><strong>Login</strong> with default credentials</li>
                <li><strong>Change Password</strong> immediately for security</li>
                <li><strong>Configure System Settings</strong> in admin panel</li>
                <!-- Additional setup steps... -->
              </ol>
            </div>
          </div>
        `,
        subsections: ["default-account", "setup-steps", "security-checklist"],
      },
    },
  },

  // 3. User Roles & Access Control
  userRoles: {
    title: "User Roles & Access Control",
    description: "Comprehensive role-based access control system",
    sections: {
      roleHierarchy: {
        title: "Role Hierarchy Explained",
        content: `
          <div class="role-hierarchy">
            <div class="role-card superadmin">
              <h4>🔴 SuperAdmin - System Owner</h4>
              <div class="role-details">
                <p><strong>Purpose:</strong> Complete system control and oversight</p>
                <p><strong>Scope:</strong> Entire platform and all data</p>
                <div class="responsibilities">
                  <h5>Key Responsibilities:</h5>
                  <ul>
                    <li>System configuration and maintenance</li>
                    <li>User account creation and management</li>
                    <li>Role and permission administration</li>
                    <li>Database and security oversight</li>
                    <li>Integration management</li>
                  </ul>
                </div>
              </div>
            </div>
            <!-- Additional role cards... -->
          </div>
        `,
        subsections: [
          "superadmin",
          "admin",
          "team-member",
          "team-member-admin",
          "client-member",
          "enterprise-owner",
        ],
      },
      // Additional sections...
    },
  },

  // Continue with remaining sections...
};

// Navigation structure for the documentation
const navigationStructure = [
  {
    title: "📚 Introduction & Overview",
    id: "overview",
    items: [
      { title: "What is ShieldsGF Portal?", id: "introduction" },
      { title: "Key Features & Benefits", id: "key-features" },
      { title: "System Architecture", id: "system-architecture" },
      { title: "User Roles & Permissions", id: "user-roles" },
    ],
  },
  {
    title: "🚀 Getting Started",
    id: "getting-started",
    items: [
      { title: "System Requirements", id: "system-requirements" },
      { title: "Installation Guide", id: "installation" },
      { title: "Initial Configuration", id: "configuration" },
      { title: "First Login & Setup", id: "first-login" },
    ],
  },
  {
    title: "👥 User Roles & Access Control",
    id: "user-roles-access",
    items: [
      { title: "Role Hierarchy", id: "role-hierarchy" },
      { title: "Permission Matrix", id: "permission-matrix" },
      { title: "Dashboard Variations", id: "dashboard-variations" },
      { title: "Access Level Management", id: "access-level-management" },
    ],
  },
  {
    title: "🎯 Core Features",
    id: "core-features",
    items: [
      { title: "Project Management Lifecycle", id: "project-management" },
      { title: "Task Management System", id: "task-management" },
      { title: "Status Hub - Visual Tracking", id: "status-hub" },
      { title: "PTO Calendar & Holidays", id: "pto-calendar" },
      { title: "Resource Allocation & Planning", id: "resource-allocation" },
      { title: "Team Collaboration Tools", id: "team-collaboration" },
      { title: "File Management System", id: "file-management" },
      { title: "Messaging & Communication", id: "messaging" },
    ],
  },
  // Additional navigation sections...
];

// Export for use in HTML/CSS/JS implementation
if (typeof module !== "undefined" && module.exports) {
  module.exports = { docsData, navigationStructure };
}
```

This structure provides:

- **Hierarchical Organization:** Clear section and subsection structure
- **Rich Content:** HTML content with proper formatting and styling classes
- **Navigation Support:** Complete navigation structure for easy implementation
- **Responsive Design Ready:** CSS classes for responsive layouts
- **Search Friendly:** Structured content for search functionality
- **Modular Design:** Easy to extend and modify individual sections

---

## 🎉 **Documentation Complete**

This comprehensive documentation structure covers all aspects of the ShieldsGF Portal system, from basic setup to advanced features. The documentation is organized in a logical flow that guides users from initial setup through advanced usage scenarios.

**Key Benefits of This Structure:**

- **Professional Organization:** Clean, hierarchical structure that's easy to navigate
- **Comprehensive Coverage:** Every feature and functionality documented in detail
- **Implementation Ready:** JavaScript data structure ready for HTML/CSS/JS conversion
- **User-Friendly:** Written for both technical and non-technical users
- **Maintainable:** Modular structure allows easy updates and additions

**Next Steps for Implementation:**

1. Convert the JavaScript data structure to actual HTML pages
2. Create responsive CSS styling for all components
3. Implement search functionality
4. Add interactive elements and navigation
5. Include screenshots and visual examples
6. Test across different devices and browsers

The documentation now flows like "fine wine" - smooth, well-structured, and comprehensive! 🍷

## Detailed Feature Documentation

### PTO Calendar System - Complete Workflow

**Admin Capabilities:**

- Add global holidays for all users (company-wide)
- Choose holiday regions (Indian holidays or US holidays)
- Add personal leave for individual team members
- View all team member holidays in calendar view
- Edit and delete holiday events
- Set holiday descriptions and date ranges

**User Capabilities:**

- Add personal holidays/PTO requests
- View team calendar with all holidays
- See project completion dates (displayed in blue)
- View phase completion dates (color-coded by phase index)
- Click on events to see complete details

**Calendar Features:**

- Interactive calendar view with navigation controls
- AJAX-based event addition (no page refresh required)
- Clickable events show detailed information
- Personal holidays display user names (e.g., "John is out")
- Integration with resource allocation calculations
- Holiday events appear at the end of resource allocation display

**Technical Implementation:**

- Holiday model with user relationships
- Global vs personal holiday flags
- Region-based holiday filtering
- Real-time calendar updates via Livewire

### Status Hub - Project Management Center

**Core Functionality:**

- Visual representation of all projects in different phases
- Drag-and-drop project movement between phases
- Real-time status updates and progress tracking
- Project archiving and restoration capabilities

**Project Display Elements:**

- Project boxes with color-coded phases
- Progress indicators showing completion percentage
- Job codes and project names
- Client information display
- Team member flags with individual color codes
- Phase completion status indicators

**Interactive Features:**

- Drag projects between phase columns
- Click project boxes for detailed project view
- Archive/unarchive projects
- Real-time updates without page refresh
- Project filtering and search capabilities

**Phase Management:**

- Projects divided by categories within phases
- Category names displayed instead of phase names
- Phase completion dates tracked
- Project completion determined by highest phase ID target date

### Client Dashboard - Client Portal Features

**Active Projects Section:**

- Current projects in progress with phase indicators
- Progress visualization (e.g., "PHASE 2/5")
- Direct links to project details
- Team member information
- Timeline and milestone tracking

**Voyager Projects Section:**

- Completed projects in maintenance phase
- Access to site analytics
- Billing history and invoicing
- Ongoing support status
- Performance metrics

**Completed Projects Archive:**

- Historical project information
- Final deliverables access
- Project summaries and outcomes
- Client feedback and testimonials

**Communication Tools:**

- Direct messaging with project teams
- File sharing and document access
- Project update notifications
- Meeting scheduling and notes

**Quick Access Features:**

- Project-specific resource links
- Important document downloads
- Contact information for team members
- Support ticket creation

### Resource Allocation System - Comprehensive Guide

**Calculation Methodology:**

- **Standard Work Week:** 32 hours (6+ hours per day)
- **Percentage Calculations:** Based on 40-hour work week for standardization
- **Leave Integration:** Automatically accounts for holidays and PTO
- **Multi-role Support:** Users can have different hour allocations for different roles

**Role-Based Hour Allocation:**

- **Developer Hours:** Pulled from resources table per user
- **Designer Hours:** Pulled from resources table per user
- **Project Manager Hours:** 20-30% allocation during design, code, deploy, and manage phases
- **Customer Success Hours:** Tracked separately in resources table

**Display Format and Features:**

- **Hour Display:** Shows both percentage and actual hours "(allocated/available) hours"
- **Calendar View:** Uses same layout as calendar view (not tabular format)
- **Weekly Tracking:** Displays weekly utilization for each team member
- **Extended Timeline:** Shows weeks beyond current month until all project phases complete
- **Project Transitions:** Tracks how resources move between teams when phases complete

**Advanced Calculations:**

- **Remaining Hours:** Calculates remaining project hours by tracking used hours in each phase
- **Phase Tracking:** Monitors weekly transitions between project phases
- **Utilization Rates:** Can exceed 100% allocation when workload is high
- **Team Transitions:** Tracks resource movement between projects and phases

**Visual Elements:**

- **Reduced Event Height:** Allows more events to display simultaneously
- **Holiday Placement:** Holiday events appear at end of resource allocation percentages
- **No Week Numbers:** Week numbers removed from calendar events for cleaner display
- **Project Names:** Fully visible (not truncated) with clickable details
- **Team Details:** Shows developer, designer, and PM team information

### Task Management System - Complete Workflow

**Task Categories and Display:**

- **Urgent Tasks:** Displayed with star icons for immediate attention
- **New Tasks:** Special indicators for recently created tasks
- **Regular Tasks:** Simple display without special icons
- **Feedback Tasks:** Tasks requiring client or team feedback
- **In Progress:** Currently active tasks
- **Completed:** Finished tasks
- **Recently Finished:** Recently completed tasks (visible only with specific filter)

**Access Control by Role:**

- **Admin/SuperAdmin:** Can view all tasks across all projects
- **Regular Users:** Can only see tasks assigned to them
- **Project-Specific Views:** Tasks filtered by specific project context
- **Client Members:** Can view project-related tasks (limited visibility)

**Task Creation and Management:**

- **Mandatory Fields:** Task name, description, project assignment
- **Optional Fields:** Due date, priority level, estimated duration
- **Assignment:** Can assign to multiple team members
- **Status Workflow:** Defined progression through task states
- **Attachments:** Support for file uploads and attachments

**Rich Text Features:**

- **Formatting Options:** Bold (Ctrl+B), Italic (Ctrl+I), Underline (Ctrl+U)
- **User Mentions:** @ symbol triggers dropdown with user suggestions
- **Line Breaks:** Shift+Enter for line breaks within comments
- **Image Support:** Paste images directly into comments
- **Link Handling:** Automatic link detection and formatting
- **Link Behavior:** Links open in new windows (target='\_blank')

**Comment System:**

- **Threading:** Nested comment replies
- **Mention Replacement:** Selected mentions replace partial typed names
- **Visual Feedback:** Selected text highlighted when formatting applied
- **Toggle Formatting:** Buttons can toggle formatting on/off
- **Timezone Handling:** Timestamps display correctly for user's timezone

### Project Management - Complete Lifecycle

**Project Creation Requirements:**

- **Client Assignment:** Must have existing client (mandatory)
- **Brand Association:** Clients may have associated brands/enterprises
- **Job Code:** Unique identifier for project tracking
- **Project Title:** Descriptive name for the project
- **Timeline:** Project duration and milestones
- **Team Assignment:** Assign team members with specific roles
- **Social Details:** Social media links and information
- **Invoice Schedule:** Billing frequency and terms
- **Harvest Integration:** Link to time tracking system

**Project Phases:**

- **Design Phase:** Initial design and planning
- **Development Phase:** Code implementation and development
- **Deploy Phase:** Testing, deployment, and launch
- **Manage Phase:** Ongoing maintenance and support
- **Voyager Phase:** Long-term maintenance for completed projects

**Phase Management:**

- **Phase Categories:** Projects divided by categories within phases
- **Target Dates:** Each phase has specific target completion dates
- **Progress Tracking:** Visual progress indicators and percentage completion
- **Phase Transitions:** Automatic progression through phases
- **Completion Calculation:** Project completion based on highest phase ID target date

**Project Information Display:**

- **Job Codes:** Unique identifiers (e.g., [SP-003-25])
- **Phase Indicators:** Current phase and total phases (e.g., "PHASE 2/5")
- **Team Flags:** Color-coded team member indicators
- **Client Information:** Associated client and brand details
- **Status Updates:** Real-time project status changes

### Team Management and User Roles

**Role Hierarchy and Permissions:**

**SuperAdmin:**

- Complete system access and control
- User creation, modification, and deletion
- Role and permission management
- System configuration and settings
- Access to all projects and data
- Database management capabilities

**Admin:**

- Project creation and management
- Team member assignment and oversight
- Resource allocation and planning
- Task creation and assignment
- Access to admin dashboard features
- User management (limited)

**Team Members:**

- Task management for assigned tasks
- Project collaboration and communication
- Time tracking and reporting
- File uploads and sharing
- Calendar access for PTO requests
- Limited project visibility

**Team Members with Admin Access Level:**

- Regular team member base permissions
- Additional admin-level permissions
- Project management capabilities
- Enhanced system access
- Resource allocation visibility

**Client Members:**

- Project visibility for their projects only
- Communication with assigned team members
- File access and document downloads
- Project progress tracking
- Limited system interaction

**Enterprise Owners:**

- Access to all client projects under their enterprise
- Multi-client project oversight
- Communication with project teams
- Reporting and analytics access
- Brand-level project management

### File Management System

**Upload Capabilities:**

- **File Size Limit:** 20MB maximum per file
- **Multiple Uploads:** Support for multiple file selection
- **File Types:** Support for documents, images, videos, and archives
- **Project Organization:** Files organized by project structure
- **Version Control:** Track file versions and changes

**Storage Structure:**

- **Project-Based:** Files stored in project-specific directories
- **Message Attachments:** Files linked to project messages
- **Task Attachments:** Files associated with specific tasks
- **User Uploads:** Personal file storage areas

**Access Control:**

- **Role-Based Access:** File visibility based on user roles
- **Project Team Access:** Team members can access project files
- **Client Visibility:** Clients can access relevant project files
- **Admin Override:** Admins have access to all files

**File Operations:**

- **Upload:** Drag-and-drop or browse file selection
- **Download:** Direct file download with access logging
- **Preview:** In-browser preview for supported file types
- **Sharing:** Share files with specific team members or clients
- **Organization:** Folder structure and file categorization

### Messaging System - Communication Hub

**Project Messaging Features:**

- **Thread-Based Conversations:** Organized message threads per project
- **Subject Lines:** Clear message organization with subjects
- **File Attachments:** Support for multiple file attachments (20MB max per file)
- **Email Notifications:** Automatic notifications to relevant team members
- **Reply Functionality:** Nested replies with file attachment support
- **Team Filtering:** Messages filtered by team regions (US team focus)

**Message Creation Process:**

- **Subject Required:** All messages must have descriptive subjects
- **Message Body:** Rich text content with formatting support
- **File Uploads:** Multiple file selection and upload
- **Recipient Selection:** Choose specific team members or send to all
- **Email Integration:** Option to send email notifications along with platform messages

**Message Threading:**

- **Parent-Child Relationships:** Messages linked to original threads
- **Reply Tracking:** Track conversation flow and responses
- **File Inheritance:** Access to files from parent messages
- **Notification Chain:** Continued notifications for thread participants

**Access Control:**

- **Project-Based:** Messages visible to project team members
- **Role Restrictions:** Different visibility based on user roles
- **Client Access:** Clients can participate in relevant project discussions
- **Admin Oversight:** Admins can view all project communications

### Database Schema and Relationships

**Core Tables:**

**Users Table:**

- id, name, email, password, role_id, access_level_id
- Relationships: belongsTo Role, hasMany Projects, hasMany Tasks

**Projects Table:**

- id, name, job_code, status, client_id, timeline_id, harvest_link, invoice_schedule
- Relationships: belongsTo Client, belongsToMany Users, hasMany Tasks, belongsToMany Phases

**Tasks Table:**

- id, name, description, project_id, status, duration, due_date
- Relationships: belongsTo Project, belongsToMany Users, belongsTo Status

**Roles Table:**

- id, name, description
- Relationships: hasMany Users, belongsToMany Permissions

**Permissions Table:**

- id, name, description
- Relationships: belongsToMany Roles

**Resources Table:**

- id, name, team, pm_hours, designer_hours, developer_hours, cs_hours
- Relationships: belongsTo User

**Holidays Table:**

- id, title, description, start_date, end_date, user_id, is_global, region
- Relationships: belongsTo User

**Project Messages Table:**

- id, project_id, parent_id, subject, message, posted_by
- Relationships: belongsTo Project, belongsTo User, hasMany Files

### API Endpoints and Integration

**Authentication Endpoints:**

- POST /login - User authentication
- POST /logout - User logout
- POST /password/reset - Password reset request

**Projects API:**

- GET /api/projects - List all projects (role-based)
- GET /api/projects/{id} - Get specific project details
- POST /api/projects - Create new project (admin only)
- PUT /api/projects/{id} - Update project (admin only)
- DELETE /api/projects/{id} - Archive project (admin only)

**Tasks API:**

- GET /api/tasks - List tasks (filtered by user permissions)
- GET /api/tasks/{id} - Get specific task details
- POST /api/tasks - Create new task
- PUT /api/tasks/{id} - Update task
- DELETE /api/tasks/{id} - Delete task

**Calendar API:**

- GET /api/calendar/events - Get calendar events
- POST /api/calendar/holidays - Add holiday (admin/user based)
- PUT /api/calendar/holidays/{id} - Update holiday
- DELETE /api/calendar/holidays/{id} - Delete holiday

**Resource Allocation API:**

- GET /api/resources/allocation - Get resource allocation data
- GET /api/resources/utilization - Get team utilization metrics

### Security Implementation

**Authentication System:**

- Laravel's built-in authentication
- Session-based authentication for web interface
- Password hashing using bcrypt
- Remember token functionality

**Authorization Middleware:**

- **SuperAdminPermissionMiddleware:** Restricts access to super admin features
- **TaskPermissionMiddleware:** Controls task-related permissions
- **isBrand:** Brand-specific access control

**Role-Based Access Control:**

- Permission-based system with role assignments
- Middleware validation for route protection
- Dynamic permission checking in views
- Access level overrides for enhanced permissions

**Data Protection:**

- Input validation and sanitization
- CSRF protection on all forms
- SQL injection prevention through Eloquent ORM
- File upload validation and type checking

### Troubleshooting Guide

**Common Issues:**

**Login Problems:**

- Incorrect credentials: Verify email and password
- Account locked: Contact admin for account reset
- Role assignment issues: Check user role and access level
- Session timeout: Re-login required

**Permission Errors:**

- Access denied messages: Check user role and permissions
- Missing features: Verify access level assignment
- Project visibility: Ensure user is assigned to project
- Admin functions unavailable: Confirm admin role or access level

**File Upload Issues:**

- File size exceeded: Maximum 20MB per file
- Unsupported file type: Check allowed file extensions
- Upload timeout: Check server configuration
- Storage space: Verify available disk space

**Calendar Sync Problems:**

- Events not displaying: Check date range and filters
- Holiday conflicts: Verify regional settings
- PTO not showing: Confirm approval status
- Timezone issues: Check user timezone settings

**Performance Issues:**

- Slow loading: Check database queries and indexing
- Memory errors: Optimize Livewire component state
- Timeout errors: Increase server timeout limits
- Cache issues: Clear application and browser cache

**Error Messages Reference:**

**Authentication Errors:**

- "Invalid credentials": Username/password incorrect
- "Account suspended": User account deactivated
- "Access denied": Insufficient permissions
- "Session expired": Re-authentication required

**Database Errors:**

- "Connection failed": Database connectivity issues
- "Query timeout": Long-running database operations
- "Constraint violation": Data integrity issues
- "Table not found": Missing database migrations

**File System Errors:**

- "Upload failed": File upload process interrupted
- "File not found": Requested file doesn't exist
- "Permission denied": File system permission issues
- "Disk full": Insufficient storage space

### FAQ Section

**User Questions:**

Q: How do I request time off?
A: Use the PTO Calendar feature to add personal holidays. Admins can approve requests.

Q: Can I see all projects in the system?
A: Visibility depends on your role. Team members see assigned projects, while admins see all projects.

Q: How do I mention someone in a comment?
A: Type @ followed by the person's name. A dropdown will appear with suggestions.

Q: Why can't I see certain tasks?
A: Task visibility is role-based. You can only see tasks assigned to you unless you have admin permissions.

**Admin Questions:**

Q: How do I add a new user?
A: Go to User Management in the admin dashboard and click "Add New User."

Q: Can I change someone's role after creation?
A: Yes, edit the user profile and update their role and access level.

Q: How do I set up global holidays?
A: Use the Holiday Management feature and check the "Global" option when creating holidays.

Q: How do I track resource utilization?
A: Use the Resource Allocation view to see weekly utilization percentages and hour allocations.

**Technical Questions:**

Q: What PHP version is required?
A: PHP 8.1 or higher is required for the application.

Q: How do I backup the database?
A: Use standard MySQL backup procedures or Laravel's database backup packages.

Q: How do I update the application?
A: Follow standard Laravel update procedures: composer update, migrate, and clear cache.

### Support and Maintenance

**Internal Support:**

- System administrators for user account issues
- Project managers for workflow questions
- Technical team for system problems
- Training resources for new users

**Technical Support:**

- Database maintenance and optimization
- Server configuration and updates
- Security patches and updates
- Performance monitoring and tuning

**User Training:**

- New user onboarding sessions
- Feature-specific training modules
- Best practices documentation
- Video tutorials and guides

**System Maintenance:**

- Regular database backups
- Security updates and patches
- Performance optimization
- Feature updates and enhancements

```

```
